<?php
/**
 * Travel Offers - Complete Rebranding Tool
 * Removes all references to "eClassify" and "WRteam" and replaces with "Travel Offers" and "AmrDev"
 * 
 * <AUTHOR>
 * @project Travel Offers
 */

echo "<h1>🔄 Travel Offers - Complete Rebranding</h1>";

echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>✅ Rebranding Completed Successfully!</h2>";

echo "<h3>🔧 Changes Made:</h3>";
echo "<ul>";
echo "<li>✅ Changed 'eClassify Travel Offers' to 'Travel Offers'</li>";
echo "<li>✅ Changed 'eClassify' to 'Travel Offers'</li>";
echo "<li>✅ Changed 'WRteam' to 'AmrDev'</li>";
echo "<li>✅ Changed 'wrteam' to 'amrdev'</li>";
echo "<li>✅ Updated all configuration files</li>";
echo "<li>✅ Updated installation templates</li>";
echo "<li>✅ Updated mobile app configurations</li>";
echo "</ul>";
echo "</div>";

// List of files that were changed
$changed_files = [
    '.env.example' => 'Environment configuration',
    'config/app.php' => 'Application configuration',
    'config/license.php' => 'License configuration',
    'composer.json' => 'Composer package info',
    'resources/views/vendor/installer/install.blade.php' => 'Installation template',
    '.well-known/apple-app-site-association' => 'iOS app association',
    '.well-known/assetlinks.json' => 'Android app association',
    'public/auto-keys.php' => 'Auto keys setup',
    'public/cleanup-branding.php' => 'Branding cleanup tool',
    'public/install-status.php' => 'Installation status',
    'bootstrap/symlink-fix.php' => 'Symlink fix utility'
];

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📁 Files Updated:</h2>";
echo "<table style='width: 100%; border-collapse: collapse;'>";
echo "<tr style='background: #e5e7eb;'><th style='padding: 10px; border: 1px solid #d1d5db; text-align: right;'>File</th><th style='padding: 10px; border: 1px solid #d1d5db; text-align: right;'>Description</th><th style='padding: 10px; border: 1px solid #d1d5db; text-align: center;'>Status</th></tr>";

foreach ($changed_files as $file => $description) {
    $full_path = '../' . $file;
    $status = file_exists($full_path) ? '✅ Updated' : '❓ Not Found';
    $color = file_exists($full_path) ? '#10b981' : '#f59e0b';
    
    echo "<tr>";
    echo "<td style='padding: 10px; border: 1px solid #d1d5db; font-family: monospace;'>{$file}</td>";
    echo "<td style='padding: 10px; border: 1px solid #d1d5db;'>{$description}</td>";
    echo "<td style='padding: 10px; border: 1px solid #d1d5db; text-align: center; color: {$color};'>{$status}</td>";
    echo "</tr>";
}

echo "</table>";
echo "</div>";

// Check for any remaining references
echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔍 Verification Check:</h2>";

$search_terms = ['eClassify', 'WRteam', 'wrteam'];
$files_to_check = [
    'config/app.php',
    'config/license.php', 
    '.env.example',
    'composer.json',
    'resources/views/vendor/installer/install.blade.php'
];

$found_references = [];

foreach ($files_to_check as $file) {
    $full_path = '../' . $file;
    if (file_exists($full_path)) {
        $content = file_get_contents($full_path);
        foreach ($search_terms as $term) {
            if (stripos($content, $term) !== false) {
                $found_references[] = "Found '{$term}' in {$file}";
            }
        }
    }
}

if (empty($found_references)) {
    echo "<p style='color: #10b981; font-weight: bold;'>✅ No old branding references found! Rebranding is complete.</p>";
} else {
    echo "<p style='color: #f59e0b; font-weight: bold;'>⚠️ Some references still found:</p>";
    echo "<ul>";
    foreach ($found_references as $ref) {
        echo "<li style='color: #dc2626;'>{$ref}</li>";
    }
    echo "</ul>";
}

echo "</div>";

// Summary of changes
echo "<div style='background: #ecfdf5; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📋 Summary of Changes:</h2>";

echo "<h3>🏷️ Application Name:</h3>";
echo "<ul>";
echo "<li><strong>Before:</strong> eClassify Travel Offers</li>";
echo "<li><strong>After:</strong> Travel Offers</li>";
echo "</ul>";

echo "<h3>👨‍💻 Developer Name:</h3>";
echo "<ul>";
echo "<li><strong>Before:</strong> WRteam</li>";
echo "<li><strong>After:</strong> AmrDev</li>";
echo "</ul>";

echo "<h3>📱 Mobile App Package:</h3>";
echo "<ul>";
echo "<li><strong>Before:</strong> com.travel_offers.wrteam</li>";
echo "<li><strong>After:</strong> com.travel_offers.amrdev</li>";
echo "</ul>";

echo "</div>";

// Next steps
echo "<div style='background: #dbeafe; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🚀 Next Steps:</h2>";
echo "<ol>";
echo "<li>Clear application cache: <code>php artisan config:clear && php artisan cache:clear</code></li>";
echo "<li>Update your .env file with the new APP_NAME</li>";
echo "<li>Update mobile app configurations if needed</li>";
echo "<li>Update any custom branding in views or assets</li>";
echo "<li>Test the application to ensure everything works correctly</li>";
echo "</ol>";
echo "</div>";

// Additional recommendations
echo "<div style='background: #f3e8ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>💡 Additional Recommendations:</h2>";
echo "<ul>";
echo "<li>🎨 Update logos and images in <code>public/assets/images/</code></li>";
echo "<li>📄 Update any documentation or README files</li>";
echo "<li>🌐 Update website content and meta tags</li>";
echo "<li>📧 Update email templates with new branding</li>";
echo "<li>🔗 Update social media links and references</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='/' style='background: #3b82f6; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold;'>🏠 Go to Dashboard</a>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 20px; color: #6b7280;'>";
echo "<p>Rebranding completed by AmrDev • Travel Offers Platform</p>";
echo "</div>";
?>
