/*!
 * FilePondPluginImageValidateSize 1.2.7
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit https://pqina.nl/filepond/ for details.
 */

/* eslint-disable */

!function(e,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):(e=e||self).FilePondPluginImageValidateSize=i()}(this,function(){"use strict";var e=function(e){var i=e.addFilter,t=e.utils,a=t.Type,E=t.replaceInString,n=t.isFile,_=function(e,i,t){return new Promise(function(a,E){var n=function(e){var t=e.width,n=e.height,_=i.minWidth,I=i.minHeight,l=i.maxWidth,o=i.maxHeight,T=i.minResolution,A=i.maxResolution,u=t*n;t<_||n<I?E("TOO_SMALL"):t>l||n>o?E("TOO_BIG"):null!==T&&u<T?E("TOO_LOW_RES"):null!==A&&u>A&&E("TOO_HIGH_RES"),a()};(function(e){return new Promise(function(i,t){var a=document.createElement("img");a.src=URL.createObjectURL(e),a.onerror=function(e){clearInterval(E),t(e)};var E=setInterval(function(){a.naturalWidth&&a.naturalHeight&&(clearInterval(E),URL.revokeObjectURL(a.src),i({width:a.naturalWidth,height:a.naturalHeight}))},1)})})(e).then(n).catch(function(){t?t(e,i).then(n).catch(function(){return E()}):E()})})};return i("LOAD_FILE",function(e,i){var t=i.query;return new Promise(function(i,a){if(n(e)&&function(e){return/^image/.test(e.type)}(e)&&t("GET_ALLOW_IMAGE_VALIDATE_SIZE")){var I={minWidth:t("GET_IMAGE_VALIDATE_SIZE_MIN_WIDTH"),minHeight:t("GET_IMAGE_VALIDATE_SIZE_MIN_HEIGHT"),maxWidth:t("GET_IMAGE_VALIDATE_SIZE_MAX_WIDTH"),maxHeight:t("GET_IMAGE_VALIDATE_SIZE_MAX_HEIGHT"),minResolution:t("GET_IMAGE_VALIDATE_SIZE_MIN_RESOLUTION"),maxResolution:t("GET_IMAGE_VALIDATE_SIZE_MAX_RESOLUTION")},l=t("GET_IMAGE_VALIDATE_SIZE_MEASURE");_(e,I,l).then(function(){i(e)}).catch(function(i){var n=i?{TOO_SMALL:{label:t("GET_IMAGE_VALIDATE_SIZE_LABEL_IMAGE_SIZE_TOO_SMALL"),details:t("GET_IMAGE_VALIDATE_SIZE_LABEL_EXPECTED_MIN_SIZE")},TOO_BIG:{label:t("GET_IMAGE_VALIDATE_SIZE_LABEL_IMAGE_SIZE_TOO_BIG"),details:t("GET_IMAGE_VALIDATE_SIZE_LABEL_EXPECTED_MAX_SIZE")},TOO_LOW_RES:{label:t("GET_IMAGE_VALIDATE_SIZE_LABEL_IMAGE_RESOLUTION_TOO_LOW"),details:t("GET_IMAGE_VALIDATE_SIZE_LABEL_EXPECTED_MIN_RESOLUTION")},TOO_HIGH_RES:{label:t("GET_IMAGE_VALIDATE_SIZE_LABEL_IMAGE_RESOLUTION_TOO_HIGH"),details:t("GET_IMAGE_VALIDATE_SIZE_LABEL_EXPECTED_MAX_RESOLUTION")}}[i]:{label:t("GET_IMAGE_VALIDATE_SIZE_LABEL_FORMAT_ERROR"),details:e.type};a({status:{main:n.label,sub:i?E(n.details,I):n.details}})})}else i(e)})}),{options:{allowImageValidateSize:[!0,a.BOOLEAN],imageValidateSizeLabelFormatError:["Image type not supported",a.STRING],imageValidateSizeMeasure:[null,a.FUNCTION],imageValidateSizeMinResolution:[null,a.INT],imageValidateSizeMaxResolution:[null,a.INT],imageValidateSizeLabelImageResolutionTooLow:["Resolution is too low",a.STRING],imageValidateSizeLabelImageResolutionTooHigh:["Resolution is too high",a.STRING],imageValidateSizeLabelExpectedMinResolution:["Minimum resolution is {minResolution}",a.STRING],imageValidateSizeLabelExpectedMaxResolution:["Maximum resolution is {maxResolution}",a.STRING],imageValidateSizeMinWidth:[1,a.INT],imageValidateSizeMinHeight:[1,a.INT],imageValidateSizeMaxWidth:[65535,a.INT],imageValidateSizeMaxHeight:[65535,a.INT],imageValidateSizeLabelImageSizeTooSmall:["Image is too small",a.STRING],imageValidateSizeLabelImageSizeTooBig:["Image is too big",a.STRING],imageValidateSizeLabelExpectedMinSize:["Minimum size is {minWidth} × {minHeight}",a.STRING],imageValidateSizeLabelExpectedMaxSize:["Maximum size is {maxWidth} × {maxHeight}",a.STRING]}}};return"undefined"!=typeof window&&void 0!==window.document&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:e})),e});
