{"Create Categories": "Create Categories", "Edit Categories": "Edit Categories", "Back to All Categories": "Back to All Categories", "Name": "Name", "Parent Category": "Parent Category", "Select a Category": "Select a Category", "Description": "Description", "Active": "Active", "Image": "Image", "Custom Field": "Custom Field", "Custom Fields": "Custom Fields", "Create Custom Field": "Create Custom Field", "Edit Custom Field Values": "Edit Custom Field Values", "Sub Category": "Sub Category", "Back To Category": "Back To Category", "ID": "ID", "Action": "Action", "Browse File": "Browse File", "Icon (use 256 x 256 size for better view)": "Icon (use 256 x 256 size for better view)", "Save and Back": "Save and Back", "Add Subcategory": "Add Subcategory", "Add Category": "Add Category", "To change the order, Drag the Table column Up & Down": "To change the order, Drag the Table column Up & Down", "Subcategories": "Subcategories", "Change Password": "Change Password", "Current Password": "Current Password", "New Password": "New Password", "Confirm Password": "Confirm Password", "Change": "Change", "Change Profile": "Change Profile", "Profile": "Profile", "Email": "Email", "Field Name": "Field Name", "Field Type": "Field Type", "Number Input": "Number Input", "Text Input": "Text Input", "File Input": "File Input", "Radio": "Radio", "Dropdown": "Dropdown", "Checkboxes": "Checkboxes", "(use 256 x 256 size for better view)": "(use 256 x 256 size for better view)", "Required": "Required", "Add Options": "Add Options", "Add Values": "Add Values", "Close": "Close", "All": "All", "Type": "Type", "Users": "Users", "Mobile": "Mobile", "Address": "Address", "Total Post": "Total Post", "Status": "Status", "Create Feature Section": "Create Feature Section", "Add Feature Section": "Add Feature Section", "Title": "Title", "Filters": "Filters", "Most Liked": "Most Liked", "Most Viewed": "Most Viewed", "Price Criteria": "Price Criteria", "Category Criteria": "Category Criteria", "Minimum Price": "Minimum Price", "Maximum Price": "Maximum Price", "Select Style for APP Section": "Select Style for APP Section", "Style": "Style", "Sequence": "Sequence", "Edit feature Section": "Edit feature Section", "Dashboard": "Dashboard", "Ads Listing": "Ads Listing", "Categories": "Categories", "Items Management": "Items Management", "Package Management": "Package Management", "Item Listing Package": "Item Listing Package", "Advertisement Package": "Advertisement Package", "Home Screen Management": "Home Screen Management", "Feature Section": "Feature Section", "Reports Management": "Reports Management", "Report Reasons": "Report Reasons", "User Reports": "User Reports", "Promotional Management": "Promotional Management", "Send Notification": "Send Notification", "Slider": "Slide<PERSON>", "Customers": "Customers", "System Settings": "System Settings", "Settings": "Settings", "System Update": "System Update", "Staff Management": "Staff Management", "Hi, Admin": "<PERSON>, <PERSON><PERSON>", "Total Customers": "Total Customers", "Total Items": "Total Items", "Featured Sections": "Featured Sections", "Recent Items": "Recent Items", "Category": "Category", "Added By": "Added By", "Price": "Price", "Total Categories": "Total Categories", "Total Custom Fields": "Total Custom Fields", "Min Price": "<PERSON>", "Max price": "Max price", "Value": "Value", "Are you sure": "Are you sure", "You wont be able to revert this": "You wont be able to revert this", "Yes Delete": "Yes Delete", "Cancel": "Cancel", "Back to Custom Fields": "Back to Custom Fields", "This will be applied only for": "This will be applied only for", "text": "text", "number": "number", "textarea": "textarea", "and": "and", "Field Values": "Field Values", "Field Length (Min)": "Field Length (Min)", "Field Length (Max)": "Field Length (Max)", "Items": "Items", "Watermark Image": "Watermark Image", "Latitude": "Latitude", "Longitude": "Longitude", "Contact": "Contact", "Video Link": "Video Link", "Created At": "Created At", "Updated At": "Updated At", "User ID": "User ID", "Category ID": "Category ID", "Likes": "<PERSON>s", "Clicks": "<PERSON>licks", "Item Details": "<PERSON><PERSON>", "No.": "No.", "Under Review": "Under Review", "Approve": "Approve", "Reject": "Reject", "Days": "Days", "Discount Price": "Discount Price", "Package Name": "Package Name", "Package Price": "Package Price", "Unlimited": "Unlimited", "Limited": "Limited", "Item Limit": "<PERSON><PERSON>", "Add Package": "Add Package", "Save": "Save", "Number": "Number", "Item": "<PERSON><PERSON>", "Third Party Link": "Third Party Link", "OR": "OR", "Select Category": "Select Category", "Submit": "Submit", "Reason": "Reason", "Edit Reason": "Edit Reason", "User": "User", "Select User": "Select User", "Selected Only": "Selected Only", "Message": "Message", "Select Item": "Select Item", "Message Type": "Message Type", "Please Select Notification First": "Please Select Notification First", "Go to settings": "Go to settings", "About Us": "About Us", "Terms & Conditions": "Terms & Conditions", "Privacy Policy": "Privacy Policy", "Firebase Settings": "Firebase Settings", "Languages": "Languages", "Payment Gateways": "Payment Gateways", "Company Details": "Company Details", "Company Name": "Company Name", "Contact Number": "Contact Number", "More Setting": "More Setting", "Default Language": "Default Language", "Currency Symbol": "Currency Symbol", "IOS Version": "IOS Version", "Android Version": "Android Version", "Maintenance Mode": "Maintenance Mode", "Place API Key": "Place API Key", "Number With Suffix": "Number With Suffix", "Notification FCM Key": "Notification FCM Key", "Favicon Icon": "Favicon Icon", "Company Logo": "Company Logo", "Login Page Image": "Login Page Image", "Images": "Images", "Payment Gateways Settings": "Payment Gateways Settings", "Stripe Setting": "<PERSON>e Setting", "Stripe Currency Symbol": "Stripe Currency Symbol", "Stripe Secret key": "Stripe Secret key", "Stripe Publishable key": "Stripe Publishable key", "Stripe Webhook Secret": "Stripe Webhook Secret", "Stripe Webhook URL": "Stripe Webhook URL", "Add Language": "Add Language", "Language Name": "Language Name", "Language Code": "Language Code", "RTL": "RTL", "File For Admin Panel": "File For Admin Panel", "File For App": "File For App", "Sample for Admin Panel": "Sample for Admin Panel", "Sample For App": "Sample For App", "Edit Language": "Edit Language", "Api Key": "Api Key", "Auth Domain": "Auth Domain", "Project Id": "Project Id", "Storage Bucket": "Storage Bucket", "Messaging Sender Id": "Messaging Sender Id", "App Id": "App Id", "Measurement Id": "Measurement Id", "Role": "Role", "Role Management": "Role Management", "Create New Role": "Create New Role", "Please select at least one user": "Please select at least one user", "Invalid Login Credentials": "Invalid Login Credentials", "User logged-in successfully": "User logged-in successfully", "Profile Updated Successfully": "Profile Updated Successfully", "You already have purchased this package": "You already have purchased this package", "Package Purchased Successfully": "Package Purchased Successfully", "User is allowed to create Item": "User is allowed to create Item", "User is not allowed to create Item": "User is not allowed to create Item", "No Active Package found for Item Creation": "No Active Package found for Item Creation", "Item Added Successfully": "Item Added Successfully", "Item Fetched Successfully": "<PERSON><PERSON> Fetched Successfully", "Item Deleted Successfully": "Item Deleted Successfully", "Item Status Updated Successfully": "Item Status Updated Successfully", "Notification fetched successfully": "Notification fetched successfully", "Language file not found": "Language file not found", "Invalid JSON format in the language file": "Invalid JSON format in the language file", "Data Fetched Successfully": "Data Fetched Successfully", "Your Package will be activated within 10 Minutes": "Your Package will be activated within 10 Minutes", "Your Transaction is Completed. Ads wil be credited to your account within 30 minutes.": "Your Transaction is Completed. Ads wil be credited to your account within 30 minutes.", "Payment Cancelled / Declined": "Payment Cancelled / Declined", "Item is already featured": "It<PERSON> is already featured", "Featured Item Created Successfully": "Featured Item Created Successfully", "Item added to Favourite": "Item added to Favourite", "Item remove from Favourite": "Item remove from Favourite", "Already Reported": "Already Reported", "Report Submitted Successfully": "Report Submitted Successfully", "Payment is not Enabled": "Payment is not Enabled", "Payment Transactions Fetched": "Payment Transactions Fetched", "Item Offer Created Successfully": "<PERSON><PERSON> Created Successfully", "Chat List Fetched Successfully": "Chat List Fetched Successfully", "Message Fetched Successfully": "Message Fetched Successfully", "User is deactivated. Please Contact the administrator": "User is deactivated. Please Contact the administrator", "User Packages": "User Packages", "Payment Transactions": "Payment Transactions", "User Name": "User Name", "Start Date": "Start Date", "End Date": "End Date", "Total Limit": "Total Limit", "Used Limit": "Used Limit", "Payment Status": "Payment Status", "Payment Signature": "Payment Signature", "Payment ID": "Payment ID", "Order ID": "Order ID", "Payment Gateway": "Payment Gateway", "Amount": "Amount", "Country": "Country", "State": "State", "City": "City", "Other Images": "Other Images", "Stripped Price": "Stripped Price", "Back": "Back", "in English": "in English", "Log Viewer": "Log Viewer", "Find Errors in your System": "Find Errors in your System", "Admob": "Admob", "Banner Ad": "Banner Ad", "sa": "sa"}