<?php
// اختبار شامل لنظام الصور
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل لنظام الصور</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .status-box { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1, h2 { color: #333; }
        .test-item { margin: 15px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        code { background: #f8f9fa; padding: 2px 5px; border-radius: 3px; }
        .path-info { font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار شامل لنظام الصور</h1>
        
        <?php
        // اختبار 1: التحقق من وجود symbolic link
        echo '<div class="test-item">';
        echo '<h2>1️⃣ اختبار Symbolic Link</h2>';
        $storageLink = '../public/storage';
        if (file_exists($storageLink)) {
            if (is_link($storageLink)) {
                echo '<div class="status-box success">✅ Symbolic link موجود وصحيح</div>';
                echo '<div class="path-info">المسار: ' . realpath($storageLink) . '</div>';
            } else {
                echo '<div class="status-box warning">⚠️ مجلد storage موجود لكنه ليس symbolic link</div>';
            }
        } else {
            echo '<div class="status-box error">❌ Symbolic link غير موجود</div>';
            echo '<div class="status-box info">💡 قم بتشغيل: <code>php artisan storage:link</code></div>';
        }
        echo '</div>';
        
        // اختبار 2: التحقق من مجلد storage/app/public
        echo '<div class="test-item">';
        echo '<h2>2️⃣ اختبار مجلد Storage</h2>';
        $storageAppPublic = '../storage/app/public';
        if (file_exists($storageAppPublic)) {
            if (is_writable($storageAppPublic)) {
                echo '<div class="status-box success">✅ مجلد storage/app/public موجود وقابل للكتابة</div>';
            } else {
                echo '<div class="status-box warning">⚠️ مجلد storage/app/public موجود لكن غير قابل للكتابة</div>';
            }
        } else {
            echo '<div class="status-box error">❌ مجلد storage/app/public غير موجود</div>';
        }
        echo '</div>';
        
        // اختبار 3: التحقق من إعدادات Laravel
        echo '<div class="test-item">';
        echo '<h2>3️⃣ اختبار إعدادات Laravel</h2>';
        $envFile = '../.env';
        if (file_exists($envFile)) {
            $envContent = file_get_contents($envFile);
            if (strpos($envContent, 'FILESYSTEM_DISK="public"') !== false || strpos($envContent, "FILESYSTEM_DISK='public'") !== false) {
                echo '<div class="status-box success">✅ FILESYSTEM_DISK مضبوط على public</div>';
            } else {
                echo '<div class="status-box warning">⚠️ تحقق من إعداد FILESYSTEM_DISK في ملف .env</div>';
            }
        } else {
            echo '<div class="status-box error">❌ ملف .env غير موجود</div>';
        }
        echo '</div>';
        
        // اختبار 4: اختبار رفع صورة
        echo '<div class="test-item">';
        echo '<h2>4️⃣ اختبار رفع الصور</h2>';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])) {
            $uploadDir = '../storage/app/public/test/';
            $file = $_FILES['test_image'];
            
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            if ($file['error'] === 0) {
                $fileName = 'test_' . time() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
                $uploadPath = $uploadDir . $fileName;
                
                if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                    echo '<div class="status-box success">✅ تم رفع الصورة بنجاح!</div>';
                    echo '<div class="path-info">مسار الملف: storage/app/public/test/' . $fileName . '</div>';
                    echo '<div class="path-info">رابط العرض: <a href="storage/test/' . $fileName . '" target="_blank">storage/test/' . $fileName . '</a></div>';
                    
                    // عرض الصورة
                    if (file_exists('storage/test/' . $fileName)) {
                        echo '<div style="margin-top: 15px;">';
                        echo '<img src="storage/test/' . $fileName . '" alt="صورة تجريبية" style="max-width: 300px; border: 1px solid #ddd; border-radius: 5px;">';
                        echo '</div>';
                    } else {
                        echo '<div class="status-box error">❌ الصورة تم رفعها لكن لا يمكن الوصول إليها عبر الرابط</div>';
                    }
                } else {
                    echo '<div class="status-box error">❌ فشل في رفع الصورة</div>';
                }
            } else {
                echo '<div class="status-box error">❌ خطأ في الملف: ' . $file['error'] . '</div>';
            }
        }
        
        echo '<form method="POST" enctype="multipart/form-data" style="margin-top: 15px;">';
        echo '<input type="file" name="test_image" accept="image/*" required style="margin-bottom: 10px;">';
        echo '<br><button type="submit" style="background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">رفع صورة تجريبية</button>';
        echo '</form>';
        echo '</div>';
        
        // اختبار 5: عرض الصور الموجودة
        echo '<div class="test-item">';
        echo '<h2>5️⃣ الصور الموجودة</h2>';
        $testDir = '../storage/app/public/test/';
        if (file_exists($testDir)) {
            $images = glob($testDir . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
            if (!empty($images)) {
                echo '<div class="status-box info">📁 تم العثور على ' . count($images) . ' صورة</div>';
                echo '<div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 15px;">';
                foreach ($images as $image) {
                    $imageName = basename($image);
                    $imageUrl = 'storage/test/' . $imageName;
                    echo '<div style="text-align: center;">';
                    echo '<img src="' . $imageUrl . '" alt="' . $imageName . '" style="width: 100px; height: 100px; object-fit: cover; border: 1px solid #ddd; border-radius: 5px;">';
                    echo '<br><small>' . $imageName . '</small>';
                    echo '</div>';
                }
                echo '</div>';
            } else {
                echo '<div class="status-box info">📁 لا توجد صور في مجلد الاختبار</div>';
            }
        } else {
            echo '<div class="status-box info">📁 مجلد الاختبار غير موجود</div>';
        }
        echo '</div>';
        
        // نصائح وإرشادات
        echo '<div class="test-item">';
        echo '<h2>💡 نصائح مهمة</h2>';
        echo '<ul>';
        echo '<li>تأكد من تشغيل <code>php artisan storage:link</code> بعد كل تحديث للمشروع</li>';
        echo '<li>تحقق من صلاحيات مجلد storage (يجب أن يكون 755 أو 775)</li>';
        echo '<li>في النماذج، استخدم <code>url(Storage::url($image))</code> لعرض الصور</li>';
        echo '<li>في الـ views، استخدم <code>asset("storage/path/to/image")</code> للصور الثابتة</li>';
        echo '<li>تأكد من أن APP_URL في ملف .env يطابق رابط موقعك</li>';
        echo '</ul>';
        echo '</div>';
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة للوحة التحكم</a>
        </div>
    </div>
</body>
</html>
