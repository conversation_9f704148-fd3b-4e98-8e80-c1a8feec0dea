<?php

/**
 * Travel Offers - Symlink Function Fix
 * Creates symlink function if it doesn't exist
 *
 * <AUTHOR>
 * @project eClassify Travel Offers
 */

// Check if symlink function exists, if not create a dummy one
if (!function_exists('symlink')) {
    /**
     * Dummy symlink function for shared hosting compatibility
     * Falls back to copying files instead of creating symbolic links
     *
     * @param string $target
     * @param string $link
     * @return bool
     */
    function symlink($target, $link) {
        // Log that we're using the fallback method
        if (function_exists('error_log')) {
            error_log("eClassify: Using symlink fallback - copying files instead of creating symbolic link");
        }

        // If target doesn't exist, return false
        if (!file_exists($target)) {
            return false;
        }

        // If link already exists, remove it
        if (file_exists($link)) {
            if (is_dir($link)) {
                rmdir($link);
            } else {
                unlink($link);
            }
        }

        // If target is a directory, copy recursively
        if (is_dir($target)) {
            return copyDirectory($target, $link);
        }

        // If target is a file, copy it
        return copy($target, $link);
    }
}

// Helper function for recursive directory copying
if (!function_exists('copyDirectory')) {
    /**
     * Copy directory recursively
     *
     * @param string $source
     * @param string $destination
     * @return bool
     */
    function copyDirectory($source, $destination) {
        try {
            // Create destination directory
            if (!is_dir($destination)) {
                mkdir($destination, 0755, true);
            }

            // Open source directory
            $dir = opendir($source);
            if (!$dir) {
                return false;
            }

            // Copy each file/directory
            while (($file = readdir($dir)) !== false) {
                if ($file != '.' && $file != '..') {
                    $srcFile = $source . DIRECTORY_SEPARATOR . $file;
                    $destFile = $destination . DIRECTORY_SEPARATOR . $file;

                    if (is_dir($srcFile)) {
                        copyDirectory($srcFile, $destFile);
                    } else {
                        copy($srcFile, $destFile);
                    }
                }
            }

            closedir($dir);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
}

// Auto-load this file in Laravel
if (file_exists(__DIR__ . '/../vendor/autoload.php')) {
    // This file is loaded before Laravel starts
    // So symlink function will be available throughout the application
}
