<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class ItemImages extends Model {
    use HasFactory;

    protected $fillable = [
        'item_id',
        'image',
    ];

    public function getImageAttribute($image) {
        if (empty($image)) {
            return $image;
        }

        // Use StorageHelper for better compatibility
        if (class_exists('\App\Helpers\StorageHelper')) {
            return \App\Helpers\StorageHelper::url($image);
        }

        // Fallback to original method
        return url(Storage::url($image));
    }
}
