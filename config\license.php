
<?php

/**
 * eClassify Travel Offers - License Configuration
 * License verification disabled for custom deployment
 *
 * <AUTHOR>
 * @project eClassify Travel Offers
 */

return [

    /*
    |--------------------------------------------------------------------------
    | License Verification
    |--------------------------------------------------------------------------
    |
    | This option controls whether the application should verify the license
    | with CodeCanyon/Envato Market. Set to false to disable verification.
    |
    */

    'enabled' => false,  // تعطيل نظام الترخيص

    /*
    |--------------------------------------------------------------------------
    | Purchase Code
    |--------------------------------------------------------------------------
    |
    | The purchase code from CodeCanyon. Not required when verification is disabled.
    |
    */

    'purchase_code' => env('PURCHASE_CODE', ''),

    /*
    |--------------------------------------------------------------------------
    | Verification URL
    |--------------------------------------------------------------------------
    |
    | The URL used to verify the purchase code. Not used when verification is disabled.
    |
    */

    'verification_url' => env('LICENSE_VERIFICATION_URL', ''),

    /*
    |--------------------------------------------------------------------------
    | Bypass Routes
    |--------------------------------------------------------------------------
    |
    | Routes that should bypass license verification.
    |
    */

    'bypass_routes' => [
        'install.*',
        'api.*',
        'common.*',
        'public.*',
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Settings for Travel Offers
    |--------------------------------------------------------------------------
    |
    | Custom configuration for the travel offers platform.
    |
    */

    'app_name' => 'Travel Offers',
    'app_version' => '1.0.0',
    'developer' => 'AmrDev',
    'license_type' => 'Custom License',
    'deployment_type' => 'Self-Hosted',

    /*
    |--------------------------------------------------------------------------
    | Features
    |--------------------------------------------------------------------------
    |
    | Enable/disable specific features for the travel platform.
    |
    */

    'features' => [
        'subscription_verification' => false,
        'purchase_code_check' => false,
        'envato_api_check' => false,
    ],

];

