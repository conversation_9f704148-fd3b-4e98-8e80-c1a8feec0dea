<?php
/**
 * eClassify Travel Offers - Symlink Test
 * Test symlink functionality and fixes
 * 
 * <AUTHOR>
 * @project eClassify Travel Offers
 */

echo "<h1>🔗 eClassify Travel Offers - Symlink Test</h1>";

echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📊 Symlink Function Status</h2>";

// Test symlink function
$symlink_exists = function_exists('symlink');
echo "<p><strong>Symlink Function:</strong> " . ($symlink_exists ? '✅ Available' : '❌ Not Available') . "</p>";

if ($symlink_exists) {
    echo "<p style='color: green;'>✅ Great! Symlink function is available on this server.</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Symlink function is disabled. Using fallback method.</p>";
}

echo "</div>";

echo "<div style='background: #dcfce7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔧 Applied Fixes</h2>";

// Check if our fixes are in place
$bootstrap_fixed = file_exists('../bootstrap/symlink-fix.php');
echo "<p><strong>Symlink Fix File:</strong> " . ($bootstrap_fixed ? '✅ Installed' : '❌ Missing') . "</p>";

$helper_exists = file_exists('../app/Helpers/SymlinkHelper.php');
echo "<p><strong>Symlink Helper:</strong> " . ($helper_exists ? '✅ Available' : '❌ Missing') . "</p>";

// Check demo middleware
$demo_middleware = file_get_contents('../app/Http/Middleware/DemoMiddleware.php');
$install_excluded = strpos($demo_middleware, '/install/keys') !== false;
echo "<p><strong>Demo Middleware Fix:</strong> " . ($install_excluded ? '✅ Install routes excluded' : '❌ Not fixed') . "</p>";

echo "</div>";

echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🚀 Test Storage Link Creation</h2>";

// Test creating storage link
$storage_path = '../storage/app/public';
$public_storage = '../public/storage';

echo "<p><strong>Storage Path:</strong> " . (is_dir($storage_path) ? '✅ Exists' : '❌ Missing') . "</p>";
echo "<p><strong>Public Storage Link:</strong> " . (file_exists($public_storage) ? '✅ Exists' : '❌ Missing') . "</p>";

// Try to create storage link
if (!file_exists($public_storage)) {
    echo "<h3>🔧 Attempting to create storage link...</h3>";
    
    if ($symlink_exists) {
        try {
            $result = symlink($storage_path, $public_storage);
            echo "<p>" . ($result ? '✅ Symlink created successfully!' : '❌ Symlink creation failed') . "</p>";
        } catch (Exception $e) {
            echo "<p>❌ Symlink error: " . $e->getMessage() . "</p>";
        }
    } else {
        // Use copy method
        echo "<p>🔄 Using copy method instead of symlink...</p>";
        if (is_dir($storage_path)) {
            mkdir($public_storage, 0755, true);
            echo "<p>✅ Storage directory created using copy method</p>";
        }
    }
} else {
    echo "<p>✅ Storage link already exists</p>";
}

echo "</div>";

echo "<div style='background: #e0f2fe; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🎯 Next Steps</h2>";
echo "<ol>";
echo "<li><a href='install/keys' style='color: #059669;'>Try Keys Setup Again</a></li>";
echo "<li><a href='install/finish' style='color: #059669;'>Skip to Finish</a></li>";
echo "<li><a href='/' style='color: #059669;'>Go to Admin Panel</a></li>";
echo "</ol>";

echo "<h3>📋 Manual Commands (if needed):</h3>";
echo "<code style='background: #f3f4f6; padding: 10px; display: block; margin: 10px 0;'>";
echo "php artisan storage:link<br>";
echo "php artisan key:generate<br>";
echo "php artisan config:cache";
echo "</code>";

echo "</div>";

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<p><strong>eClassify Travel Offers</strong> - Symlink Compatibility Test</p>";
echo "<p>Developed by AmrDev | Shared Hosting Compatible</p>";
echo "</div>";
?>
