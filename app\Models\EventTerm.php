<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EventTerm extends Model
{
    use HasFactory;

    protected $fillable = [
        'term_id',
        'target_id',
        'create_user',
        'update_user'
    ];

    public function event()
    {
        return $this->belongsTo(Event::class, 'target_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
