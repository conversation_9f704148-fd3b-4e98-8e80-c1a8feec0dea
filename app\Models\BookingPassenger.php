<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BookingPassenger extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'flight_id',
        'flight_seat_id',
        'booking_id',
        'seat_type',
        'email',
        'first_name',
        'last_name',
        'phone',
        'dob',
        'price',
        'id_card',
        'nationality',
        'gender',
        'object_model',
        'object_id',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'dob' => 'datetime',
        'price' => 'decimal:2'
    ];

    public function flight()
    {
        return $this->belongsTo(Flight::class);
    }

    public function flightSeat()
    {
        return $this->belongsTo(FlightSeat::class);
    }

    public function booking()
    {
        return $this->belongsTo(FlightBooking::class);
    }

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
