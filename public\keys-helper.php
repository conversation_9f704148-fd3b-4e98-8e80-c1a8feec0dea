<?php
/**
 * eClassify Travel Offers - Keys Setup Helper
 * Helps with application keys and URL configuration
 * 
 * <AUTHOR>
 * @project eClassify Travel Offers
 */

echo "<h1>🔑 eClassify Travel Offers - Keys Setup Helper</h1>";

echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📊 Current Configuration</h2>";

// Check current URL
$current_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'];
echo "<p><strong>Current Domain:</strong> $current_url</p>";
echo "<p><strong>Suggested App URL:</strong> https://admin.demo5150.com</p>";

// Check if .env exists
$env_exists = file_exists('../.env');
echo "<p><strong>.env File:</strong> " . ($env_exists ? '✅ Exists' : '❌ Missing') . "</p>";

if ($env_exists) {
    $env_content = file_get_contents('../.env');
    $has_app_key = strpos($env_content, 'APP_KEY=') !== false;
    $has_app_url = strpos($env_content, 'APP_URL=') !== false;
    
    echo "<p><strong>APP_KEY:</strong> " . ($has_app_key ? '✅ Set' : '❌ Missing') . "</p>";
    echo "<p><strong>APP_URL:</strong> " . ($has_app_url ? '✅ Set' : '❌ Missing') . "</p>";
}

echo "</div>";

echo "<div style='background: #dcfce7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔧 What This Step Does</h2>";
echo "<ul>";
echo "<li>✅ Generates Laravel APP_KEY for encryption</li>";
echo "<li>✅ Sets the correct APP_URL for your domain</li>";
echo "<li>✅ Creates storage symlink for file access</li>";
echo "<li>✅ Configures API connection settings</li>";
echo "<li>✅ Prepares the application for production</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>⚠️ Important Notes</h2>";
echo "<ul>";
echo "<li><strong>App URL:</strong> Must match your domain exactly</li>";
echo "<li><strong>HTTPS:</strong> Recommended for production</li>";
echo "<li><strong>Storage Link:</strong> Required for file uploads</li>";
echo "<li><strong>API Keys:</strong> Will be generated automatically</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #e0f2fe; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🚀 Quick Actions</h2>";

// Auto-fill form
echo "<h3>📝 Pre-filled Form:</h3>";
echo "<form method='post' action='install/keys' style='background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;'>";
echo "<input type='hidden' name='_token' value='" . csrf_token() . "'>";
echo "<label for='app_url' style='display: block; margin-bottom: 5px; font-weight: bold;'>App URL *</label>";
echo "<input type='url' name='app_url' id='app_url' value='https://admin.demo5150.com' required style='width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin-bottom: 15px;'>";
echo "<button type='submit' style='background: #059669; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Generate Keys & Continue</button>";
echo "</form>";

echo "<h3>🔗 Manual Links:</h3>";
echo "<p><a href='install/keys' style='background: #0369a1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Go to Keys Page</a></p>";
echo "<p><a href='install/finish' style='background: #7c3aed; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Skip to Finish</a></p>";
echo "</div>";

// Check Laravel commands
echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔧 System Check</h2>";

echo "<h3>PHP Functions:</h3>";
$required_functions = ['openssl_random_pseudo_bytes', 'hash', 'base64_encode', 'file_put_contents'];
foreach ($required_functions as $func) {
    echo "<p><strong>$func:</strong> " . (function_exists($func) ? '✅ Available' : '❌ Missing') . "</p>";
}

echo "<h3>Directory Permissions:</h3>";
$directories = [
    '../storage' => 'Storage Directory',
    '../bootstrap/cache' => 'Bootstrap Cache',
    '../public' => 'Public Directory'
];

foreach ($directories as $dir => $name) {
    $writable = is_writable($dir);
    echo "<p><strong>$name:</strong> " . ($writable ? '✅ Writable' : '❌ Not Writable') . "</p>";
}

echo "</div>";

// Helper function for CSRF token (simplified)
function csrf_token() {
    return 'dummy-token-for-demo';
}

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<p><strong>eClassify Travel Offers</strong> - Keys & Configuration Setup</p>";
echo "<p>Developed by AmrDev | Secure Key Generation</p>";
echo "</div>";
?>
