
STOP!

Before you open an issue:

1. Make sure that your issue is a BUG or FEATURE REQUEST.  General usage and troubleshooting questions should be directed to the [forums](https://forums.select2.org) or [Stack Overflow](https://stackoverflow.com/questions/tagged/jquery-select2).  Issues asking for general support WILL BE CLOSED automatically.
2. Search the current issues, both open and closed, for a similar issue. If the bug is still present but the relevant issue has been closed, you may ask us to reopen the issue. Duplicate issues will be closed automatically.
3. Make sure that you are using the latest stable version of Select2 (see the [release history](https://github.com/select2/select2/releases)). Old minor/patch versions will not be supported.

If you are CERTAIN that it is appropriate to open a new issue, you must:

1. Create and link to a [minimal, complete, and verifiable](https://stackoverflow.com/help/mcve) example by cloning our [JSBin template](http://jsbin.com/goqagokoye/edit?html,js,output). Code dumps, zip files, etc are NOT acceptable.
2. Any additional code snippets should be formatted using [Markdown code fences](https://learn.userfrosting.com/troubleshooting/getting-help#use-markdown-to-format-blocks-of-code) and properly indented. Poorly formatted code is difficult to read and reflects badly on you as a programmer.
3. Include relevant screenshots or animations, if possible. Drag your screenshots directly into this text box. They will automatically be uploaded and the Markdown to display them will be generated.  Avoid third-party image hosts, or links which require extra clicks to view the image.
4. If you are not conversationally proficient in English, do NOT just post a machine translation (e.g. Google Translate) to GitHub. Get help in crafting your question, either via the [forums](https://forums.select2.org) or in [chat](https://webchat.freenode.net/?channels=select2). If all else fails, you may post your bug report or feature request in your native language, with a machine translation below that. We will tag it with `translation-needed` so that others who speak your language can find it.
5. State the steps needed to reproduce the problem.
6. Mention your version of Select2, as well as the browser(s) and operating system(s) in which the problem occurs.
7. Clear out this placeholder text.

Issues that ignore these rules will be closed without further comment!
