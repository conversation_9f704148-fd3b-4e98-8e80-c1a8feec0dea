.chat-application .chat-app {
    border: 1px solid #dfe3e7;
    border-radius: 5px;
    height: calc(100vh - 9rem)
}

.chat-app .chat-app-right {
    padding-left: 0
}

.chat-app .chat-app-header .person-name {
    font-size: 1.2rem
}

.chat-app .chat-app-body .left {
    border-right: 1px solid #e9ecef
}

.chat-app .chat-app-body ul {
    overflow-y: auto;
    padding-left: 0
}

.chat-app .chat-app-body .the-contact {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    list-style: none;
    margin-right: 0;
    padding: 1.5rem 2rem;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none
}

.chat-app .chat-app-body .the-contact.active {
    background-color: #087C7C
}

.chat-app .chat-app-body .the-contact.active .message-excerpt,
.chat-app .chat-app-body .the-contact.active .person-name {
    color: #fff
}

.chat-app .chat-app-body .the-contact .notification-count {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%)
}

.chat-app .chat-app-body .the-contact:hover {
    cursor: pointer
}

.chat-app .chat-app-body .the-contact:not(.active):hover {
    background-color: #e9ecef;
    transition: all .1s
}

.chat-app .chat-app-body .the-contact .verified-badge {
    height: 1.4em;
    padding: 0;
    width: 1.4em
}

.chat-app .chat-app-body .the-contact .person-name {
    align-items: center;
    color: #495057;
    display: flex;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0
}

.chat-app .chat-app-body .the-contact .message-excerpt {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 0
}

.chat-app .chat-app-footer {
    height: 100%;
    position: relative
}

.chat-app .chat-app-footer .input-message-wrapper {
    background-color: #fff;
    bottom: 0;
    padding: 1rem;
    position: absolute;
    right: 0;
    width: 100%
}

.chat-app .messages {
    display: flex;
    flex-direction: column;
    width: 100%
}

.chat-app .messages .message {
    background-color: #dee2e6;
    border-bottom-left-radius: 1rem;
    border-bottom-right-radius: 1rem;
    border-top-right-radius: 1rem;
    float: left;
    margin: 1rem;
    max-width: 600px;
    padding: .8rem
}

.chat-app .messages .message.message-right {
    background-color: #697ecb;
    border-top-left-radius: 1rem;
    border-top-right-radius: 0;
    color: #fff;
    float: right
}

@media screen and (max-width:1200px) {
    .chat-app .chat-app-left {
        right: 100%
    }

    .chat-app .chat-app-right {
        bottom: 0;
        left: 0;
        padding-left: 15px !important;
        position: absolute;
        right: 0;
        top: 0
    }

    .chat-app .chat-app-right .chat-app-header>div {
        padding: 0 2rem
    }
}
