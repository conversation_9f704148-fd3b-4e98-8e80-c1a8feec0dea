<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TicketCategory extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'name',
        'name_ar',
        'price',
        'total_quantity',
        'available_quantity',
        'description',
        'description_ar',
        'benefits',
        'section',
        'row_range',
        'is_vip',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'benefits' => 'array',
        'is_vip' => 'boolean'
    ];

    protected $appends = ['display_name', 'display_description', 'is_available'];

    /**
     * Get display name based on locale
     */
    public function getDisplayNameAttribute()
    {
        return app()->getLocale() === 'ar' ? ($this->name_ar ?: $this->name) : $this->name;
    }

    /**
     * Get display description based on locale
     */
    public function getDisplayDescriptionAttribute()
    {
        return app()->getLocale() === 'ar' ? ($this->description_ar ?: $this->description) : $this->description;
    }

    /**
     * Check if tickets are available
     */
    public function getIsAvailableAttribute()
    {
        return $this->available_quantity > 0;
    }

    /**
     * Get event
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get ticket bookings
     */
    public function ticketBookings()
    {
        return $this->hasMany(TicketBooking::class);
    }

    /**
     * Get confirmed bookings
     */
    public function confirmedBookings()
    {
        return $this->hasMany(TicketBooking::class)->where('status', 'confirmed');
    }

    /**
     * Reserve tickets
     */
    public function reserveTickets($quantity)
    {
        if ($this->available_quantity >= $quantity) {
            $this->decrement('available_quantity', $quantity);
            return true;
        }
        return false;
    }

    /**
     * Release tickets
     */
    public function releaseTickets($quantity)
    {
        $this->increment('available_quantity', $quantity);
    }

    /**
     * Get sold quantity
     */
    public function getSoldQuantityAttribute()
    {
        return $this->total_quantity - $this->available_quantity;
    }

    /**
     * Get sold percentage
     */
    public function getSoldPercentageAttribute()
    {
        if ($this->total_quantity == 0) return 0;
        return round(($this->sold_quantity / $this->total_quantity) * 100, 2);
    }

    /**
     * Scope for available categories
     */
    public function scopeAvailable($query)
    {
        return $query->where('available_quantity', '>', 0);
    }

    /**
     * Scope for VIP categories
     */
    public function scopeVip($query)
    {
        return $query->where('is_vip', true);
    }

    /**
     * Scope by price range
     */
    public function scopeByPriceRange($query, $minPrice, $maxPrice)
    {
        return $query->whereBetween('price', [$minPrice, $maxPrice]);
    }

    /**
     * Get creator user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    /**
     * Get updater user
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
