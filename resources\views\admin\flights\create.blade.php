@extends('layouts.admin')

@section('title', 'إضافة رحلة جديدة')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-plus"></i>
                        إضافة رحلة جديدة
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.flights.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            العودة للقائمة
                        </a>
                    </div>
                </div>

                <form action="{{ route('admin.flights.store') }}" method="POST" id="flight-form">
                    @csrf
                    <div class="card-body">
                        <div class="row">
                            <!-- معلومات الرحلة الأساسية -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="flight_number">رقم الرحلة <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('flight_number') is-invalid @enderror" 
                                           id="flight_number" name="flight_number" value="{{ old('flight_number') }}" 
                                           placeholder="مثال: KU101" required>
                                    @error('flight_number')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="airline_id">شركة الطيران <span class="text-danger">*</span></label>
                                    <select class="form-control select2 @error('airline_id') is-invalid @enderror" 
                                            id="airline_id" name="airline_id" required>
                                        <option value="">اختر شركة الطيران</option>
                                        @foreach($airlines as $airline)
                                            <option value="{{ $airline->id }}" {{ old('airline_id') == $airline->id ? 'selected' : '' }}>
                                                {{ $airline->name }} ({{ $airline->code }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('airline_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- مطارات المغادرة والوصول -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="airport_from">مطار المغادرة <span class="text-danger">*</span></label>
                                    <select class="form-control select2 @error('airport_from') is-invalid @enderror" 
                                            id="airport_from" name="airport_from" required>
                                        <option value="">اختر مطار المغادرة</option>
                                        @foreach($airports as $airport)
                                            <option value="{{ $airport->id }}" {{ old('airport_from') == $airport->id ? 'selected' : '' }}>
                                                {{ $airport->name }} ({{ $airport->code }}) - {{ $airport->city }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('airport_from')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="airport_to">مطار الوصول <span class="text-danger">*</span></label>
                                    <select class="form-control select2 @error('airport_to') is-invalid @enderror" 
                                            id="airport_to" name="airport_to" required>
                                        <option value="">اختر مطار الوصول</option>
                                        @foreach($airports as $airport)
                                            <option value="{{ $airport->id }}" {{ old('airport_to') == $airport->id ? 'selected' : '' }}>
                                                {{ $airport->name }} ({{ $airport->code }}) - {{ $airport->city }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('airport_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- تواريخ وأوقات المغادرة والوصول -->
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="departure_date">تاريخ المغادرة <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('departure_date') is-invalid @enderror" 
                                           id="departure_date" name="departure_date" value="{{ old('departure_date') }}" required>
                                    @error('departure_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="departure_time">وقت المغادرة <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control @error('departure_time') is-invalid @enderror" 
                                           id="departure_time" name="departure_time" value="{{ old('departure_time') }}" required>
                                    @error('departure_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="arrival_date">تاريخ الوصول <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('arrival_date') is-invalid @enderror" 
                                           id="arrival_date" name="arrival_date" value="{{ old('arrival_date') }}" required>
                                    @error('arrival_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="arrival_time">وقت الوصول <span class="text-danger">*</span></label>
                                    <input type="time" class="form-control @error('arrival_time') is-invalid @enderror" 
                                           id="arrival_time" name="arrival_time" value="{{ old('arrival_time') }}" required>
                                    @error('arrival_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات التسعير -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="base_price">السعر الأساسي (د.ك) <span class="text-danger">*</span></label>
                                    <input type="number" step="0.001" class="form-control @error('base_price') is-invalid @enderror" 
                                           id="base_price" name="base_price" value="{{ old('base_price') }}" 
                                           placeholder="0.000" required>
                                    @error('base_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="discount_percentage">نسبة الخصم (%)</label>
                                    <input type="number" step="0.01" min="0" max="100" 
                                           class="form-control @error('discount_percentage') is-invalid @enderror" 
                                           id="discount_percentage" name="discount_percentage" 
                                           value="{{ old('discount_percentage', 0) }}" placeholder="0.00">
                                    @error('discount_percentage')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="total_seats">إجمالي المقاعد <span class="text-danger">*</span></label>
                                    <input type="number" min="1" class="form-control @error('total_seats') is-invalid @enderror" 
                                           id="total_seats" name="total_seats" value="{{ old('total_seats', 180) }}" required>
                                    @error('total_seats')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- معلومات إضافية -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="aircraft_type">نوع الطائرة</label>
                                    <input type="text" class="form-control @error('aircraft_type') is-invalid @enderror" 
                                           id="aircraft_type" name="aircraft_type" value="{{ old('aircraft_type') }}" 
                                           placeholder="مثال: Boeing 777">
                                    @error('aircraft_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status">حالة الرحلة</label>
                                    <select class="form-control @error('status') is-invalid @enderror" id="status" name="status">
                                        <option value="scheduled" {{ old('status') == 'scheduled' ? 'selected' : '' }}>مجدول</option>
                                        <option value="boarding" {{ old('status') == 'boarding' ? 'selected' : '' }}>صعود</option>
                                        <option value="departed" {{ old('status') == 'departed' ? 'selected' : '' }}>مغادر</option>
                                        <option value="arrived" {{ old('status') == 'arrived' ? 'selected' : '' }}>وصل</option>
                                        <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>ملغي</option>
                                        <option value="delayed" {{ old('status') == 'delayed' ? 'selected' : '' }}>متأخر</option>
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="description">وصف الرحلة</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3" 
                                              placeholder="معلومات إضافية عن الرحلة...">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" 
                                           value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        رحلة مميزة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            حفظ الرحلة
                        </button>
                        <button type="button" class="btn btn-success" id="save-and-add-seats">
                            <i class="fas fa-chair"></i>
                            حفظ وإضافة المقاعد
                        </button>
                        <a href="{{ route('admin.flights.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });

    // Auto-calculate arrival date
    $('#departure_date, #departure_time').on('change', function() {
        const departureDate = $('#departure_date').val();
        const departureTime = $('#departure_time').val();
        
        if (departureDate && departureTime) {
            // Set arrival date to same day by default (can be changed manually)
            if (!$('#arrival_date').val()) {
                $('#arrival_date').val(departureDate);
            }
        }
    });

    // Validate that arrival is after departure
    $('#arrival_date, #arrival_time').on('change', function() {
        validateDateTime();
    });

    // Save and add seats button
    $('#save-and-add-seats').on('click', function() {
        $('#flight-form').append('<input type="hidden" name="redirect_to_seats" value="1">');
        $('#flight-form').submit();
    });
});

function validateDateTime() {
    const departureDate = $('#departure_date').val();
    const departureTime = $('#departure_time').val();
    const arrivalDate = $('#arrival_date').val();
    const arrivalTime = $('#arrival_time').val();
    
    if (departureDate && departureTime && arrivalDate && arrivalTime) {
        const departure = new Date(departureDate + 'T' + departureTime);
        const arrival = new Date(arrivalDate + 'T' + arrivalTime);
        
        if (arrival <= departure) {
            alert('وقت الوصول يجب أن يكون بعد وقت المغادرة');
            $('#arrival_date').focus();
            return false;
        }
    }
    return true;
}

// Form validation before submit
$('#flight-form').on('submit', function(e) {
    if (!validateDateTime()) {
        e.preventDefault();
        return false;
    }
    
    // Check if departure and arrival airports are different
    const fromAirport = $('#airport_from').val();
    const toAirport = $('#airport_to').val();
    
    if (fromAirport === toAirport) {
        alert('مطار المغادرة والوصول لا يمكن أن يكونا نفس المطار');
        e.preventDefault();
        return false;
    }
});
</script>
@endsection
