{"name": "select2", "description": "Select2 is a jQuery based replacement for select boxes. It supports searching, remote data sets, and infinite scrolling of results.", "main": ["dist/js/select2.js", "src/scss/core.scss"], "license": "MIT", "repository": {"type": "git", "url": "**************:select2/select2.git"}, "homepage": "https://github.com/ivaynberg/select2", "version": "4.0.13", "_release": "4.0.13", "_resolution": {"type": "version", "tag": "4.0.13", "commit": "45f2b83ceed5231afa7b3d5b12b58ad335edd82e"}, "_source": "https://github.com/ivaynberg/select2.git", "_target": "^4.0.13", "_originalSource": "select2", "_direct": true}