<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FlightBooking;
use App\Models\Flight;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FlightBookingController extends Controller
{
    /**
     * Display a listing of flight bookings
     */
    public function index(Request $request)
    {
        $query = FlightBooking::with(['flight.airline', 'flight.fromAirport', 'flight.toAirport', 'seats']);

        // Apply filters
        if ($request->filled('booking_reference')) {
            $query->where('booking_reference', 'like', '%' . $request->booking_reference . '%');
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $bookings = $query->latest()->paginate(25);

        // Statistics
        $totalBookings = FlightBooking::count();
        $confirmedBookings = FlightBooking::where('status', 'confirmed')->count();
        $pendingBookings = FlightBooking::where('status', 'pending')->count();
        $cancelledBookings = FlightBooking::where('status', 'cancelled')->count();

        // Top destinations
        $topDestinations = FlightBooking::select(
                'flights.airport_to',
                DB::raw('COUNT(*) as bookings_count'),
                DB::raw('SUM(flight_bookings.total_price) as total_revenue'),
                'airports.name as to_airport_name'
            )
            ->join('flights', 'flight_bookings.flight_id', '=', 'flights.id')
            ->join('airports', 'flights.airport_to', '=', 'airports.id')
            ->where('flight_bookings.status', '!=', 'cancelled')
            ->groupBy('flights.airport_to', 'airports.name')
            ->orderBy('bookings_count', 'desc')
            ->limit(5)
            ->get();

        // Monthly data for charts
        $monthlyData = FlightBooking::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as count')
            )
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('count', 'month')
            ->toArray();

        $monthlyLabels = [];
        $monthlyBookings = [];
        for ($i = 1; $i <= 12; $i++) {
            $monthlyLabels[] = date('M', mktime(0, 0, 0, $i, 1));
            $monthlyBookings[] = $monthlyData[$i] ?? 0;
        }

        return view('admin.flight-bookings.index', compact(
            'bookings', 'totalBookings', 'confirmedBookings', 
            'pendingBookings', 'cancelledBookings', 'topDestinations',
            'monthlyLabels', 'monthlyData'
        ));
    }

    /**
     * Display the specified booking
     */
    public function show(FlightBooking $flightBooking)
    {
        $flightBooking->load(['flight.airline', 'flight.fromAirport', 'flight.toAirport', 'seats', 'passengers']);

        return view('admin.flight-bookings.show', compact('flightBooking'));
    }

    /**
     * Confirm a booking
     */
    public function confirm(FlightBooking $flightBooking)
    {
        try {
            $flightBooking->update([
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'confirmed_by' => auth()->id(),
            ]);

            // Send confirmation email/SMS here

            return response()->json([
                'success' => true,
                'message' => 'تم تأكيد الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تأكيد الحجز: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel a booking
     */
    public function cancel(Request $request, FlightBooking $flightBooking)
    {
        try {
            $flightBooking->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancelled_by' => auth()->id(),
                'cancellation_reason' => $request->reason,
            ]);

            // Free up the seats
            $flightBooking->seats()->update(['status' => 'available']);

            // Process refund if payment was made
            if ($flightBooking->payment_status === 'paid') {
                // Process refund logic here
                $flightBooking->update(['payment_status' => 'refunded']);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء الحجز: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate ticket PDF
     */
    public function ticket(FlightBooking $flightBooking)
    {
        $flightBooking->load(['flight.airline', 'flight.fromAirport', 'flight.toAirport', 'seats', 'passengers']);

        // Generate PDF ticket here
        // This would require a PDF library like DomPDF or TCPDF

        return view('admin.flight-bookings.ticket', compact('flightBooking'));
    }

    /**
     * Export bookings to Excel
     */
    public function export(Request $request)
    {
        $query = FlightBooking::with(['flight.airline', 'flight.fromAirport', 'flight.toAirport']);

        // Apply same filters as index
        if ($request->filled('booking_reference')) {
            $query->where('booking_reference', 'like', '%' . $request->booking_reference . '%');
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $bookings = $query->get();

        // Export to Excel logic here
        // This would require Laravel Excel package

        $filename = 'flight_bookings_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        return response()->json([
            'success' => true,
            'message' => 'تم تصدير البيانات بنجاح',
            'download_url' => route('admin.flight-bookings.download', ['file' => $filename])
        ]);
    }

    /**
     * Update booking status
     */
    public function updateStatus(Request $request, FlightBooking $flightBooking)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled,completed',
        ]);

        try {
            $flightBooking->update([
                'status' => $request->status,
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث حالة الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحديث: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Send booking confirmation
     */
    public function sendConfirmation(FlightBooking $flightBooking)
    {
        try {
            // Send confirmation email/SMS logic here
            
            return response()->json([
                'success' => true,
                'message' => 'تم إرسال تأكيد الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء الإرسال: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get booking statistics
     */
    public function statistics()
    {
        $stats = [
            'total_bookings' => FlightBooking::count(),
            'confirmed_bookings' => FlightBooking::where('status', 'confirmed')->count(),
            'pending_bookings' => FlightBooking::where('status', 'pending')->count(),
            'cancelled_bookings' => FlightBooking::where('status', 'cancelled')->count(),
            'total_revenue' => FlightBooking::where('payment_status', 'paid')->sum('total_price'),
            'today_bookings' => FlightBooking::whereDate('created_at', today())->count(),
            'this_month_bookings' => FlightBooking::whereMonth('created_at', date('m'))->count(),
        ];

        return response()->json($stats);
    }
}
