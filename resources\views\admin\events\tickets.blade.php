@extends('layouts.admin')

@section('title', 'إدارة تذاكر الفعالية - ' . $event->title)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-ticket-alt"></i>
                        إدارة تذاكر الفعالية: {{ $event->title }}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.events.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            العودة للفعاليات
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- معلومات الفعالية -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> معلومات الفعالية</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>المكان:</strong> {{ $event->venue->name }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>المدينة:</strong> {{ $event->venue->city }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>السعة:</strong> {{ number_format($event->venue->capacity) }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>الحالة:</strong> 
                                        <span class="badge badge-{{ $event->status == 'upcoming' ? 'primary' : ($event->status == 'ongoing' ? 'success' : 'secondary') }}">
                                            {{ $event->status == 'upcoming' ? 'قادمة' : ($event->status == 'ongoing' ? 'جارية' : 'مكتملة') }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات التذاكر -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-ticket-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي التذاكر</span>
                                    <span class="info-box-number">{{ $totalTickets }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">مباع</span>
                                    <span class="info-box-number">{{ $soldTickets }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">متاح</span>
                                    <span class="info-box-number">{{ $availableTickets }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-dollar-sign"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الإيرادات</span>
                                    <span class="info-box-number">{{ number_format($totalRevenue, 3) }} د.ك</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات إدارة التذاكر -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="btn-toolbar" role="toolbar">
                                <div class="btn-group mr-2" role="group">
                                    <button type="button" class="btn btn-primary" onclick="addTicketCategory()">
                                        <i class="fas fa-plus"></i>
                                        إضافة فئة تذاكر
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="generateTickets()">
                                        <i class="fas fa-magic"></i>
                                        إنشاء تذاكر تلقائي
                                    </button>
                                </div>
                                <div class="btn-group mr-2" role="group">
                                    <button type="button" class="btn btn-warning" onclick="bulkUpdatePrices()">
                                        <i class="fas fa-dollar-sign"></i>
                                        تحديث الأسعار
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="exportTickets()">
                                        <i class="fas fa-download"></i>
                                        تصدير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فئات التذاكر -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>فئات التذاكر</h5>
                                </div>
                                <div class="card-body">
                                    @if($ticketCategories->count() > 0)
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>اسم الفئة</th>
                                                        <th>السعر</th>
                                                        <th>الكمية</th>
                                                        <th>المباع</th>
                                                        <th>المتاح</th>
                                                        <th>الوصف</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($ticketCategories as $category)
                                                    <tr>
                                                        <td>
                                                            <strong>{{ $category->name }}</strong>
                                                            @if($category->is_vip)
                                                                <span class="badge badge-warning">VIP</span>
                                                            @endif
                                                        </td>
                                                        <td>
                                                            <strong>{{ number_format($category->price, 3) }} د.ك</strong>
                                                            @if($category->discount_percentage > 0)
                                                                <div>
                                                                    <small class="text-success">
                                                                        خصم {{ $category->discount_percentage }}%
                                                                    </small>
                                                                </div>
                                                            @endif
                                                        </td>
                                                        <td>{{ number_format($category->quantity) }}</td>
                                                        <td>
                                                            <span class="text-success">{{ $category->sold_count }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="text-info">{{ $category->available_count }}</span>
                                                        </td>
                                                        <td>
                                                            <small>{{ Str::limit($category->description, 50) }}</small>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm">
                                                                <button class="btn btn-warning" onclick="editTicketCategory({{ $category->id }})">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button class="btn btn-info" onclick="viewTickets({{ $category->id }})">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                @if($category->sold_count == 0)
                                                                    <button class="btn btn-danger" onclick="deleteTicketCategory({{ $category->id }})">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                @endif
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @else
                                        <div class="text-center py-5">
                                            <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                                            <h5>لا توجد فئات تذاكر محددة لهذه الفعالية</h5>
                                            <p class="text-muted">استخدم زر "إضافة فئة تذاكر" لإنشاء فئات التذاكر</p>
                                            <button type="button" class="btn btn-primary" onclick="addTicketCategory()">
                                                <i class="fas fa-plus"></i>
                                                إضافة فئة تذاكر
                                            </button>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التذاكر الفردية -->
                    @if($individualTickets->count() > 0)
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>التذاكر الفردية</h5>
                                    <div class="card-tools">
                                        <button class="btn btn-sm btn-primary" onclick="scanTicket()">
                                            <i class="fas fa-qrcode"></i>
                                            مسح QR Code
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped" id="tickets-table">
                                            <thead>
                                                <tr>
                                                    <th>رقم التذكرة</th>
                                                    <th>الفئة</th>
                                                    <th>المشتري</th>
                                                    <th>السعر</th>
                                                    <th>الحالة</th>
                                                    <th>تاريخ الشراء</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($individualTickets as $ticket)
                                                <tr>
                                                    <td>
                                                        <strong>{{ $ticket->ticket_number }}</strong>
                                                        <div>
                                                            <small class="text-muted">{{ $ticket->qr_code }}</small>
                                                        </div>
                                                    </td>
                                                    <td>{{ $ticket->ticketCategory->name }}</td>
                                                    <td>
                                                        @if($ticket->booking)
                                                            <div>{{ $ticket->booking->customer_name }}</div>
                                                            <small class="text-muted">{{ $ticket->booking->customer_email }}</small>
                                                        @else
                                                            <span class="text-muted">غير محجوز</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ number_format($ticket->price, 3) }} د.ك</td>
                                                    <td>
                                                        @switch($ticket->status)
                                                            @case('available')
                                                                <span class="badge badge-success">متاح</span>
                                                                @break
                                                            @case('sold')
                                                                <span class="badge badge-warning">مباع</span>
                                                                @break
                                                            @case('used')
                                                                <span class="badge badge-info">مستخدم</span>
                                                                @break
                                                            @case('cancelled')
                                                                <span class="badge badge-danger">ملغي</span>
                                                                @break
                                                        @endswitch
                                                    </td>
                                                    <td>
                                                        @if($ticket->booking)
                                                            {{ $ticket->booking->created_at->format('Y-m-d H:i') }}
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-info" onclick="viewTicket({{ $ticket->id }})">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="btn btn-primary" onclick="printTicket({{ $ticket->id }})">
                                                                <i class="fas fa-print"></i>
                                                            </button>
                                                            @if($ticket->status == 'sold')
                                                                <button class="btn btn-success" onclick="validateTicket({{ $ticket->id }})">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            @endif
                                                            @if(in_array($ticket->status, ['available', 'sold']))
                                                                <button class="btn btn-danger" onclick="cancelTicket({{ $ticket->id }})">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة/تعديل فئة تذاكر -->
<div class="modal fade" id="ticketCategoryModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ticketCategoryModalTitle">إضافة فئة تذاكر</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="ticketCategoryForm">
                <div class="modal-body">
                    <input type="hidden" id="category_id" name="category_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>اسم الفئة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="category_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>السعر (د.ك) <span class="text-danger">*</span></label>
                                <input type="number" step="0.001" class="form-control" id="category_price" name="price" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>الكمية <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="category_quantity" name="quantity" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>نسبة الخصم (%)</label>
                                <input type="number" step="0.01" min="0" max="100" class="form-control" id="category_discount" name="discount_percentage">
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>الوصف</label>
                        <textarea class="form-control" id="category_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="category_is_vip" name="is_vip" value="1">
                        <label class="form-check-label" for="category_is_vip">
                            فئة VIP
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    $('#tickets-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "pageLength": 50
    });
});

function addTicketCategory() {
    $('#ticketCategoryModalTitle').text('إضافة فئة تذاكر جديدة');
    $('#ticketCategoryForm')[0].reset();
    $('#category_id').val('');
    $('#ticketCategoryModal').modal('show');
}

function editTicketCategory(categoryId) {
    $.ajax({
        url: '/admin/events/ticket-categories/' + categoryId,
        type: 'GET',
        success: function(category) {
            $('#ticketCategoryModalTitle').text('تعديل فئة التذاكر');
            $('#category_id').val(category.id);
            $('#category_name').val(category.name);
            $('#category_price').val(category.price);
            $('#category_quantity').val(category.quantity);
            $('#category_discount').val(category.discount_percentage);
            $('#category_description').val(category.description);
            $('#category_is_vip').prop('checked', category.is_vip);
            $('#ticketCategoryModal').modal('show');
        }
    });
}

$('#ticketCategoryForm').on('submit', function(e) {
    e.preventDefault();
    
    const categoryId = $('#category_id').val();
    const url = categoryId ? '/admin/events/ticket-categories/' + categoryId : '{{ route("admin.events.tickets.store", $event->id) }}';
    const method = categoryId ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        type: method,
        data: $(this).serialize() + '&_token={{ csrf_token() }}',
        success: function(response) {
            if (response.success) {
                $('#ticketCategoryModal').modal('hide');
                location.reload();
            } else {
                alert('حدث خطأ: ' + response.message);
            }
        }
    });
});

function generateTickets() {
    if (confirm('هل تريد إنشاء تذاكر فردية لجميع الفئات؟')) {
        $.ajax({
            url: '{{ route("admin.events.generate-tickets", $event->id) }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + response.message);
                }
            }
        });
    }
}

function validateTicket(ticketId) {
    if (confirm('هل تريد تأكيد استخدام هذه التذكرة؟')) {
        $.ajax({
            url: '/admin/tickets/' + ticketId + '/validate',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + response.message);
                }
            }
        });
    }
}

function printTicket(ticketId) {
    window.open('/admin/tickets/' + ticketId + '/print', '_blank');
}
</script>
@endsection
