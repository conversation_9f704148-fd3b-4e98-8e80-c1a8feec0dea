/**
 * Seller Verification Modal Fix
 * إصلاح مشكلة عدم عمل الحفظ في modal seller verification
 */

$(document).ready(function() {
    console.log('🔧 Seller Verification Fix loaded');
    
    // تأكد من وجود baseurl
    if (typeof window.baseurl === 'undefined') {
        window.baseurl = window.location.origin + '/';
        console.warn('⚠️ baseurl was undefined, set to:', window.baseurl);
    }
    
    // إصلاح مشكلة الـ edit button في جدول seller verification
    $(document).on('click', '[data-events="verificationEvents"] .edit_btn', function(e) {
        e.preventDefault();
        console.log('🖱️ Edit button clicked');
        
        // الحصول على بيانات الصف من الجدول
        const $row = $(this).closest('tr');
        const rowData = $('#table_list').bootstrapTable('getData')[$row.data('index')];
        
        if (!rowData) {
            console.error('❌ Could not get row data');
            return;
        }
        
        console.log('📊 Row data:', rowData);
        
        // ملء البيانات في الـ modal
        $('#verification_status').val(rowData.status || 'pending').trigger('change');
        $('#rejection_reason').val(rowData.rejection_reason || '');
        
        // تحديد الـ action URL
        const actionUrl = window.baseurl + 'seller-verification/' + rowData.id + '/approval';
        $('#statusUpdateForm').attr('action', actionUrl);
        
        console.log('📝 Form action set to:', actionUrl);
        
        // عرض الـ modal
        $('#editStatusModal').modal('show');
        console.log('👁️ Modal shown');
    });
    
    // التعامل مع تغيير الحالة
    $(document).on('change', '#verification_status', function() {
        const status = $(this).val();
        const rejectionField = $('#rejectionReasonField');
        
        console.log('🔄 Status changed to:', status);
        
        if (status === 'rejected') {
            rejectionField.show();
            $('#rejection_reason').attr('required', true);
            console.log('👁️ Rejection reason field shown');
        } else {
            rejectionField.hide();
            $('#rejection_reason').attr('required', false);
            console.log('🙈 Rejection reason field hidden');
        }
    });
    
    // إصلاح مشكلة إرسال الـ form
    $(document).on('submit', '#statusUpdateForm', function(e) {
        e.preventDefault();
        console.log('📤 Form submitted');
        
        const $form = $(this);
        const $submitBtn = $form.find('input[type="submit"]');
        const originalBtnText = $submitBtn.val();
        const formData = new FormData(this);
        const actionUrl = $form.attr('action');
        
        if (!actionUrl) {
            console.error('❌ No action URL set');
            alert('خطأ: لم يتم تحديد رابط الإرسال');
            return;
        }
        
        console.log('📤 Sending to:', actionUrl);
        
        // تعطيل الزر أثناء الإرسال
        $submitBtn.val('جاري الحفظ...').prop('disabled', true);
        
        // طباعة بيانات الـ form
        console.log('📋 Form data:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }
        
        // إرسال AJAX request
        $.ajax({
            url: actionUrl,
            method: 'POST', // Laravel will handle _method=PUT
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('✅ Success response:', response);
                
                // إظهار رسالة نجاح
                if (typeof showSuccessToast === 'function') {
                    showSuccessToast(response.message || 'تم الحفظ بنجاح');
                } else {
                    alert(response.message || 'تم الحفظ بنجاح');
                }
                
                // إغلاق الـ modal
                $('#editStatusModal').modal('hide');
                
                // تحديث الجدول
                if (typeof $('#table_list').bootstrapTable === 'function') {
                    $('#table_list').bootstrapTable('refresh');
                }
                
                console.log('🔄 Table refreshed');
            },
            error: function(xhr, status, error) {
                console.error('❌ Error response:', xhr.responseText);
                
                let errorMessage = 'حدث خطأ أثناء الحفظ';
                
                try {
                    const response = JSON.parse(xhr.responseText);
                    errorMessage = response.message || errorMessage;
                } catch (e) {
                    console.error('Could not parse error response');
                }
                
                // إظهار رسالة خطأ
                if (typeof showErrorToast === 'function') {
                    showErrorToast(errorMessage);
                } else {
                    alert(errorMessage);
                }
            },
            complete: function() {
                // إعادة تفعيل الزر
                $submitBtn.val(originalBtnText).prop('disabled', false);
                console.log('🔄 Button re-enabled');
            }
        });
    });
    
    // إصلاح مشكلة الـ bootstrap table events
    if (typeof window.verificationEvents === 'undefined') {
        window.verificationEvents = {};
    }
    
    // تحديث الـ verificationEvents
    window.verificationEvents['click .edit_btn'] = function(e, value, row) {
        console.log('🖱️ Bootstrap table edit button clicked');
        console.log('📊 Row data:', row);
        
        // ملء البيانات في الـ modal
        $('#verification_status').val(row.status || 'pending').trigger('change');
        $('#rejection_reason').val(row.rejection_reason || '');
        
        // تحديد الـ action URL
        const actionUrl = window.baseurl + 'seller-verification/' + row.id + '/approval';
        $('#statusUpdateForm').attr('action', actionUrl);
        
        console.log('📝 Form action set to:', actionUrl);
        
        // عرض الـ modal
        $('#editStatusModal').modal('show');
        console.log('👁️ Modal shown');
    };
    
    console.log('✅ Seller Verification Fix initialized');
});

// دالة مساعدة لاختبار النظام
function testSellerVerificationModal() {
    console.log('🧪 Testing seller verification modal...');
    
    // محاكاة بيانات صف
    const testRow = {
        id: 1,
        status: 'pending',
        rejection_reason: ''
    };
    
    // استدعاء الـ event handler
    if (window.verificationEvents && window.verificationEvents['click .edit_btn']) {
        window.verificationEvents['click .edit_btn'](null, null, testRow);
    } else {
        console.error('❌ verificationEvents not found');
    }
}

// إضافة الدالة للـ window للوصول إليها من console
window.testSellerVerificationModal = testSellerVerificationModal;
