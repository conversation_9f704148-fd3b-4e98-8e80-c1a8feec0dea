<?php
/**
 * Storage Setup Tool for Shared Hosting
 * Use this when symbolic links are not supported
 */

// Include Lara<PERSON> bootstrap
require_once '../vendor/autoload.php';

$app = require_once '../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد Storage للاستضافة المشتركة</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-box { padding: 15px; margin: 15px 0; border-radius: 8px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1, h2 { color: #333; }
        .btn { background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #005a8b; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        code { background: #f8f9fa; padding: 3px 6px; border-radius: 3px; font-family: monospace; }
        .step { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .step h3 { margin-top: 0; color: #007cba; }
        ul { padding-right: 20px; }
        .path-info { font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إعداد Storage للاستضافة المشتركة</h1>
        <p>هذه الأداة تساعدك في إعداد نظام الصور عندما لا تدعم الاستضافة الـ symbolic links</p>

        <?php
        $storageAppPublic = '../storage/app/public';
        $publicStorage = '../public/storage';
        $hasSymlinkFunction = function_exists('symlink');
        $storageExists = file_exists($storageAppPublic);
        $publicStorageExists = file_exists($publicStorage);
        $isSymlink = $publicStorageExists && is_link($publicStorage);

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';

            echo '<div class="status-box info"><h3>🔄 جاري التنفيذ...</h3></div>';

            if ($action === 'create_copy') {
                // Create directory copy
                if (!$storageExists) {
                    mkdir($storageAppPublic, 0755, true);
                    file_put_contents($storageAppPublic . '/.gitkeep', '');
                }

                if ($publicStorageExists) {
                    if (is_link($publicStorage)) {
                        unlink($publicStorage);
                    } else {
                        // Remove directory recursively
                        function removeDirectory($dir) {
                            if (is_dir($dir)) {
                                $files = array_diff(scandir($dir), array('.', '..'));
                                foreach ($files as $file) {
                                    $path = $dir . '/' . $file;
                                    is_dir($path) ? removeDirectory($path) : unlink($path);
                                }
                                rmdir($dir);
                            }
                        }
                        removeDirectory($publicStorage);
                    }
                }

                // Create directory and copy files
                mkdir($publicStorage, 0755, true);

                // Copy existing files
                function copyDirectory($source, $dest) {
                    if (!is_dir($source)) return false;
                    if (!is_dir($dest)) mkdir($dest, 0755, true);

                    $files = scandir($source);
                    foreach ($files as $file) {
                        if ($file != '.' && $file != '..') {
                            $srcFile = $source . '/' . $file;
                            $destFile = $dest . '/' . $file;

                            if (is_dir($srcFile)) {
                                copyDirectory($srcFile, $destFile);
                            } else {
                                copy($srcFile, $destFile);
                            }
                        }
                    }
                    return true;
                }

                if (copyDirectory($storageAppPublic, $publicStorage)) {
                    echo '<div class="status-box success">✅ تم إنشاء نسخة من مجلد storage بنجاح!</div>';
                } else {
                    echo '<div class="status-box error">❌ فشل في نسخ الملفات</div>';
                }
            }

            if ($action === 'try_symlink') {
                if ($hasSymlinkFunction) {
                    if ($publicStorageExists) {
                        if (is_link($publicStorage)) {
                            unlink($publicStorage);
                        } else {
                            rmdir($publicStorage);
                        }
                    }

                    if (symlink($storageAppPublic, $publicStorage)) {
                        echo '<div class="status-box success">✅ تم إنشاء symbolic link بنجاح!</div>';
                    } else {
                        echo '<div class="status-box error">❌ فشل في إنشاء symbolic link</div>';
                    }
                } else {
                    echo '<div class="status-box error">❌ وظيفة symlink غير متوفرة على هذا السيرفر</div>';
                }
            }

            // Refresh status
            $publicStorageExists = file_exists($publicStorage);
            $isSymlink = $publicStorageExists && is_link($publicStorage);
        }
        ?>

        <!-- Status Check -->
        <div class="step">
            <h3>📊 حالة النظام الحالية</h3>

            <p><strong>دعم Symbolic Links:</strong>
                <?php echo $hasSymlinkFunction ? '<span style="color: green;">✅ مدعوم</span>' : '<span style="color: red;">❌ غير مدعوم</span>'; ?>
            </p>

            <p><strong>مجلد storage/app/public:</strong>
                <?php echo $storageExists ? '<span style="color: green;">✅ موجود</span>' : '<span style="color: red;">❌ غير موجود</span>'; ?>
            </p>

            <p><strong>مجلد public/storage:</strong>
                <?php
                if ($publicStorageExists) {
                    if ($isSymlink) {
                        echo '<span style="color: green;">✅ موجود (symbolic link)</span>';
                    } else {
                        echo '<span style="color: orange;">⚠️ موجود (مجلد عادي)</span>';
                    }
                } else {
                    echo '<span style="color: red;">❌ غير موجود</span>';
                }
                ?>
            </p>
        </div>

        <!-- Actions -->
        <div class="step">
            <h3>🛠️ الإجراءات المتاحة</h3>

            <form method="POST" style="margin: 10px 0;">
                <?php if ($hasSymlinkFunction): ?>
                    <button type="submit" name="action" value="try_symlink" class="btn btn-success">
                        🔗 محاولة إنشاء Symbolic Link
                    </button>
                <?php endif; ?>

                <button type="submit" name="action" value="create_copy" class="btn btn-warning">
                    📁 إنشاء نسخة من المجلد (للاستضافة المشتركة)
                </button>
            </form>
        </div>

        <!-- Instructions -->
        <div class="step">
            <h3>📋 التعليمات</h3>

            <h4>للاستضافة المشتركة (لا تدعم symbolic links):</h4>
            <ol>
                <li>اضغط على "إنشاء نسخة من المجلد"</li>
                <li>سيتم نسخ جميع الملفات من <code>storage/app/public</code> إلى <code>public/storage</code></li>
                <li>عند رفع صور جديدة، سيتم نسخها تلقائياً إلى المجلد العام</li>
            </ol>

            <h4>للاستضافة المتقدمة (تدعم symbolic links):</h4>
            <ol>
                <li>اضغط على "محاولة إنشاء Symbolic Link"</li>
                <li>إذا نجح، ستعمل الصور بشكل طبيعي</li>
                <li>إذا فشل، استخدم طريقة النسخ</li>
            </ol>
        </div>

        <!-- Test Upload -->
        <div class="step">
            <h3>🧪 اختبار رفع الصور</h3>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])): ?>
                <?php
                $uploadDir = '../storage/app/public/test/';
                $publicDir = '../public/storage/test/';
                $file = $_FILES['test_image'];

                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }

                if ($file['error'] === 0) {
                    $fileName = 'test_' . time() . '.' . pathinfo($file['name'], PATHINFO_EXTENSION);
                    $uploadPath = $uploadDir . $fileName;

                    if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
                        echo '<div class="status-box success">✅ تم رفع الصورة إلى storage بنجاح!</div>';

                        // Copy to public if not symlink
                        if (!$isSymlink && $publicStorageExists) {
                            if (!file_exists($publicDir)) {
                                mkdir($publicDir, 0755, true);
                            }
                            if (copy($uploadPath, $publicDir . $fileName)) {
                                echo '<div class="status-box success">✅ تم نسخ الصورة إلى public بنجاح!</div>';
                            }
                        }

                        $imageUrl = 'storage/test/' . $fileName;
                        echo '<div class="path-info">رابط الصورة: <a href="' . $imageUrl . '" target="_blank">' . $imageUrl . '</a></div>';

                        if (file_exists($imageUrl)) {
                            echo '<img src="' . $imageUrl . '" alt="صورة تجريبية" style="max-width: 200px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px;">';
                        } else {
                            echo '<div class="status-box error">❌ الصورة غير متاحة عبر الرابط</div>';
                        }
                    } else {
                        echo '<div class="status-box error">❌ فشل في رفع الصورة</div>';
                    }
                }
            ?>
            <?php endif; ?>

            <form method="POST" enctype="multipart/form-data">
                <input type="file" name="test_image" accept="image/*" required style="margin: 10px 0;">
                <br>
                <button type="submit" class="btn">📤 رفع صورة تجريبية</button>
            </form>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">🏠 العودة للوحة التحكم</a>
            <a href="image-test.php" class="btn">🔍 اختبار شامل للصور</a>
        </div>
    </div>
</body>
</html>
