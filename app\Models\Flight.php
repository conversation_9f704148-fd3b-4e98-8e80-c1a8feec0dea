<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Flight extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'code',
        'flight_number',
        'review_score',
        'departure_time',
        'arrival_time',
        'duration',
        'min_price',
        'airport_to',
        'airport_from',
        'airline_id',
        'aircraft_type',
        'status',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'departure_time' => 'datetime',
        'arrival_time' => 'datetime',
        'review_score' => 'decimal:1'
    ];

    protected $appends = ['duration_formatted', 'status_text'];

    /**
     * Get formatted duration
     */
    public function getDurationFormattedAttribute()
    {
        if (!$this->duration) return null;
        
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;
        
        return $hours . 'h ' . $minutes . 'm';
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'scheduled' => 'مجدولة',
            'delayed' => 'متأخرة',
            'cancelled' => 'ملغية',
            'completed' => 'مكتملة',
            default => 'غير محدد'
        };
    }

    /**
     * Get airline
     */
    public function airline()
    {
        return $this->belongsTo(Airline::class);
    }

    /**
     * Get departure airport
     */
    public function departureAirport()
    {
        return $this->belongsTo(Airport::class, 'airport_from');
    }

    /**
     * Get arrival airport
     */
    public function arrivalAirport()
    {
        return $this->belongsTo(Airport::class, 'airport_to');
    }

    /**
     * Get flight seats
     */
    public function seats()
    {
        return $this->hasMany(FlightSeat::class);
    }

    /**
     * Get available seats
     */
    public function availableSeats()
    {
        return $this->hasMany(FlightSeat::class)->where('is_available', true);
    }

    /**
     * Get bookings
     */
    public function bookings()
    {
        return $this->hasMany(FlightBooking::class);
    }

    /**
     * Get passengers
     */
    public function passengers()
    {
        return $this->hasMany(BookingPassenger::class);
    }

    /**
     * Scope for scheduled flights
     */
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    /**
     * Scope for future flights
     */
    public function scopeFuture($query)
    {
        return $query->where('departure_time', '>', now());
    }

    /**
     * Scope by route
     */
    public function scopeByRoute($query, $from, $to)
    {
        return $query->where('airport_from', $from)->where('airport_to', $to);
    }

    /**
     * Scope by date range
     */
    public function scopeByDateRange($query, $startDate, $endDate = null)
    {
        $query->whereDate('departure_time', '>=', $startDate);
        
        if ($endDate) {
            $query->whereDate('departure_time', '<=', $endDate);
        }
        
        return $query;
    }

    /**
     * Scope by airline
     */
    public function scopeByAirline($query, $airlineId)
    {
        return $query->where('airline_id', $airlineId);
    }

    /**
     * Get creator user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    /**
     * Get updater user
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
