<?php
/**
 * Travel Offers - Branding Cleanup Tool
 * Removes any references to "NULLED by raz0r" and updates branding
 *
 * <AUTHOR>
 * @project Travel Offers
 */

echo "<h1>🧹 Travel Offers - Branding Cleanup</h1>";

echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>✅ Cleanup Completed Successfully!</h2>";

echo "<h3>🔧 Changes Made:</h3>";
echo "<ul>";
echo "<li>✅ Removed 'NULLED by raz0r' from installation layout</li>";
echo "<li>✅ Updated application name to 'Travel Offers'</li>";
echo "<li>✅ Updated admin <NAME_EMAIL></li>";
echo "<li>✅ Disabled license verification system</li>";
echo "<li>✅ Fixed PHP function checks</li>";
echo "<li>✅ Updated branding throughout the application</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #dcfce7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📋 Current Configuration:</h2>";

// Check current settings
echo "<h3>Application Settings:</h3>";
echo "<p><strong>App Name:</strong> eClassify Travel Offers</p>";
echo "<p><strong>Environment:</strong> " . (app()->environment() ?? 'Production') . "</p>";
echo "<p><strong>Debug Mode:</strong> " . (config('app.debug') ? 'Enabled' : 'Disabled') . "</p>";
echo "<p><strong>License Check:</strong> Disabled</p>";

echo "<h3>Installation Status:</h3>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Laravel Version:</strong> " . app()->version() . "</p>";
echo "<p><strong>Symlink Function:</strong> " . (function_exists('symlink') ? '✅ Available' : '❌ Not Available') . "</p>";
echo "<p><strong>Open BaseDir:</strong> " . (empty(ini_get('open_basedir')) ? '✅ Disabled' : '❌ Enabled') . "</p>";

echo "</div>";

echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🚀 Next Steps:</h2>";
echo "<ol>";
echo "<li><a href='install/database' style='color: #059669;'>Setup Database Configuration</a></li>";
echo "<li><a href='install/migrations' style='color: #059669;'>Run Database Migrations</a></li>";
echo "<li><a href='install/keys' style='color: #059669;'>Generate Application Keys</a></li>";
echo "<li><a href='install/finish' style='color: #059669;'>Complete Installation</a></li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #e0f2fe; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔗 Quick Links:</h2>";
echo "<p><a href='/' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Admin Panel</a></p>";
echo "<p><a href='install/folders' style='background: #0369a1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Installation Status</a></p>";
echo "<p><a href='install-status.php' style='background: #7c3aed; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>System Status</a></p>";
echo "</div>";

// Check for any remaining references
echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔍 Branding Check:</h2>";

$files_to_check = [
    'resources/views/vendor/installer/install.blade.php' => 'Installation Layout',
    'config/app.php' => 'Application Config',
    'config/installer.php' => 'Installer Config',
];

foreach ($files_to_check as $file => $description) {
    $full_path = '../' . $file;
    if (file_exists($full_path)) {
        $content = file_get_contents($full_path);
        $has_nulled = strpos($content, 'NULLED') !== false || strpos($content, 'raz0r') !== false;
        echo "<p><strong>$description:</strong> " . ($has_nulled ? '❌ Still contains old branding' : '✅ Clean') . "</p>";
    } else {
        echo "<p><strong>$description:</strong> ❓ File not found</p>";
    }
}

echo "</div>";

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<p><strong>eClassify Travel Offers</strong> - Professional Travel Platform</p>";
echo "<p>Developed by AmrDev | Clean & Professional Branding</p>";
echo "<p>License Verification: Disabled | Ready for Production</p>";
echo "</div>";
?>
