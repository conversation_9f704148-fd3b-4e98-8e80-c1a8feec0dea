<?php

namespace App\Helpers;

use Illuminate\Support\Facades\File;

/**
 * eClassify Travel Offers - Symlink Helper
 * Alternative symlink implementation for shared hosting
 * 
 * <AUTHOR>
 * @project eClassify Travel Offers
 */
class SymlinkHelper
{
    /**
     * Create a symlink or copy files if symlink is not available
     *
     * @param string $target
     * @param string $link
     * @return bool
     */
    public static function createLink($target, $link)
    {
        // If symlink function exists, use it
        if (function_exists('symlink')) {
            try {
                return symlink($target, $link);
            } catch (\Exception $e) {
                // If symlink fails, fall back to copy
                return self::copyDirectory($target, $link);
            }
        }
        
        // If symlink doesn't exist, copy the directory
        return self::copyDirectory($target, $link);
    }
    
    /**
     * Copy directory recursively as alternative to symlink
     *
     * @param string $source
     * @param string $destination
     * @return bool
     */
    private static function copyDirectory($source, $destination)
    {
        try {
            if (!File::exists($source)) {
                return false;
            }
            
            if (!File::exists($destination)) {
                File::makeDirectory($destination, 0755, true);
            }
            
            $files = File::allFiles($source);
            
            foreach ($files as $file) {
                $relativePath = $file->getRelativePathname();
                $destinationPath = $destination . DIRECTORY_SEPARATOR . $relativePath;
                
                // Create directory if it doesn't exist
                $destinationDir = dirname($destinationPath);
                if (!File::exists($destinationDir)) {
                    File::makeDirectory($destinationDir, 0755, true);
                }
                
                // Copy the file
                File::copy($file->getPathname(), $destinationPath);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Create storage link for Laravel
     *
     * @return bool
     */
    public static function createStorageLink()
    {
        $target = storage_path('app/public');
        $link = public_path('storage');
        
        // Remove existing link/directory
        if (File::exists($link)) {
            if (is_link($link)) {
                unlink($link);
            } else {
                File::deleteDirectory($link);
            }
        }
        
        return self::createLink($target, $link);
    }
    
    /**
     * Check if storage link exists and is working
     *
     * @return bool
     */
    public static function storageLinked()
    {
        $link = public_path('storage');
        return File::exists($link);
    }
}
