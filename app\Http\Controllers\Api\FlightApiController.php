<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Flight;
use App\Models\Airline;
use App\Models\Airport;
use App\Models\FlightBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class FlightApiController extends Controller
{
    /**
     * Get list of flights with search and filters
     */
    public function index(Request $request)
    {
        try {
            $query = Flight::with(['airline', 'departureAirport', 'arrivalAirport', 'availableSeats'])
                          ->where('status', 'scheduled')
                          ->where('departure_time', '>', now());

            // Search filters
            if ($request->filled('from_airport')) {
                $query->where('airport_from', $request->from_airport);
            }

            if ($request->filled('to_airport')) {
                $query->where('airport_to', $request->to_airport);
            }

            if ($request->filled('departure_date')) {
                $query->whereDate('departure_time', $request->departure_date);
            }

            if ($request->filled('airline_id')) {
                $query->where('airline_id', $request->airline_id);
            }

            if ($request->filled('min_price')) {
                $query->where('min_price', '>=', $request->min_price);
            }

            if ($request->filled('max_price')) {
                $query->where('min_price', '<=', $request->max_price);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'departure_time');
            $sortOrder = $request->get('sort_order', 'asc');
            
            if (in_array($sortBy, ['departure_time', 'min_price', 'duration'])) {
                $query->orderBy($sortBy, $sortOrder);
            }

            $flights = $query->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $flights->items(),
                'pagination' => [
                    'current_page' => $flights->currentPage(),
                    'last_page' => $flights->lastPage(),
                    'per_page' => $flights->perPage(),
                    'total' => $flights->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الرحلات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get flight details
     */
    public function show($id)
    {
        try {
            $flight = Flight::with([
                'airline', 
                'departureAirport', 
                'arrivalAirport', 
                'seats.seatType'
            ])->find($id);

            if (!$flight) {
                return response()->json([
                    'success' => false,
                    'message' => 'الرحلة غير موجودة'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $flight
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب تفاصيل الرحلة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get airlines list
     */
    public function airlines()
    {
        try {
            $airlines = Airline::active()->orderBy('name')->get();

            return response()->json([
                'success' => true,
                'data' => $airlines
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب شركات الطيران',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get airports list
     */
    public function airports(Request $request)
    {
        try {
            $query = Airport::active()->orderBy('name');

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('city', 'like', "%{$search}%");
                });
            }

            if ($request->filled('country')) {
                $query->where('country', $request->country);
            }

            $airports = $query->get();

            return response()->json([
                'success' => true,
                'data' => $airports
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المطارات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Book a flight
     */
    public function book(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'flight_id' => 'required|exists:flights,id',
                'passengers' => 'required|array|min:1',
                'passengers.*.first_name' => 'required|string|max:255',
                'passengers.*.last_name' => 'required|string|max:255',
                'passengers.*.email' => 'required|email',
                'passengers.*.phone' => 'required|string',
                'passengers.*.dob' => 'required|date',
                'passengers.*.id_card' => 'required|string',
                'passengers.*.nationality' => 'required|string',
                'passengers.*.gender' => 'required|in:male,female',
                'passengers.*.seat_id' => 'required|exists:flight_seats,id',
                'special_requests' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $flight = Flight::find($request->flight_id);
            
            if (!$flight || $flight->status !== 'scheduled') {
                return response()->json([
                    'success' => false,
                    'message' => 'الرحلة غير متاحة للحجز'
                ], 400);
            }

            // Calculate total price
            $totalPrice = 0;
            $passengers = $request->passengers;
            
            foreach ($passengers as $passenger) {
                $seat = $flight->seats()->find($passenger['seat_id']);
                if (!$seat || !$seat->is_available) {
                    return response()->json([
                        'success' => false,
                        'message' => 'المقعد غير متاح'
                    ], 400);
                }
                $totalPrice += $seat->price;
            }

            // Create booking
            $booking = FlightBooking::create([
                'user_id' => auth()->id(),
                'flight_id' => $request->flight_id,
                'booking_reference' => FlightBooking::generateBookingReference(),
                'total_price' => $totalPrice,
                'passengers_count' => count($passengers),
                'passenger_details' => $passengers,
                'status' => 'pending',
                'payment_status' => 'pending',
                'booking_date' => now(),
                'special_requests' => $request->special_requests
            ]);

            // Reserve seats
            foreach ($passengers as $passenger) {
                $seat = $flight->seats()->find($passenger['seat_id']);
                $seat->reserve();
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الحجز بنجاح',
                'data' => [
                    'booking_reference' => $booking->booking_reference,
                    'total_price' => $totalPrice,
                    'booking_id' => $booking->id
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في إنشاء الحجز',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's flight bookings
     */
    public function myBookings(Request $request)
    {
        try {
            $query = FlightBooking::with(['flight.airline', 'flight.departureAirport', 'flight.arrivalAirport'])
                                 ->where('user_id', auth()->id());

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            $bookings = $query->orderBy('booking_date', 'desc')
                             ->paginate($request->get('per_page', 20));

            return response()->json([
                'success' => true,
                'data' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الحجوزات',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
