<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Airline;
use App\Models\Airport;
use App\Models\Flight;
use App\Models\FlightSeat;
use App\Models\SeatType;
use Carbon\Carbon;

class FlightSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create seat types
        $seatTypes = [
            [
                'code' => 'economy',
                'name' => 'Economy Class',
                'name_ar' => 'الدرجة الاقتصادية',
                'description' => 'Standard economy seating with basic amenities',
                'features' => json_encode(['Standard seat', 'In-flight meal', 'Entertainment system'])
            ],
            [
                'code' => 'business',
                'name' => 'Business Class',
                'name_ar' => 'درجة رجال الأعمال',
                'description' => 'Premium business class with enhanced comfort',
                'features' => json_encode(['Lie-flat seat', 'Premium meals', 'Priority boarding', 'Lounge access'])
            ],
            [
                'code' => 'first',
                'name' => 'First Class',
                'name_ar' => 'الدرجة الأولى',
                'description' => 'Luxury first class experience',
                'features' => json_encode(['Private suite', 'Gourmet dining', 'Personal butler', 'Spa services'])
            ]
        ];

        foreach ($seatTypes as $seatType) {
            SeatType::create(array_merge($seatType, ['create_user' => 1]));
        }

        // Create airlines
        $airlines = [
            [
                'name' => 'Kuwait Airways',
                'code' => 'KU',
                'country' => 'Kuwait',
                'description' => 'National carrier of Kuwait',
                'status' => 'publish'
            ],
            [
                'name' => 'Emirates',
                'code' => 'EK',
                'country' => 'UAE',
                'description' => 'Premium airline from Dubai',
                'status' => 'publish'
            ],
            [
                'name' => 'Qatar Airways',
                'code' => 'QR',
                'country' => 'Qatar',
                'description' => 'Award-winning airline from Doha',
                'status' => 'publish'
            ],
            [
                'name' => 'Saudi Arabian Airlines',
                'code' => 'SV',
                'country' => 'Saudi Arabia',
                'description' => 'National carrier of Saudi Arabia',
                'status' => 'publish'
            ],
            [
                'name' => 'Etihad Airways',
                'code' => 'EY',
                'country' => 'UAE',
                'description' => 'National airline of the UAE',
                'status' => 'publish'
            ]
        ];

        foreach ($airlines as $airline) {
            Airline::create(array_merge($airline, ['create_user' => 1]));
        }

        // Create airports
        $airports = [
            [
                'name' => 'Kuwait International Airport',
                'code' => 'KWI',
                'city' => 'Kuwait City',
                'country' => 'Kuwait',
                'timezone' => 'Asia/Kuwait',
                'status' => 'publish'
            ],
            [
                'name' => 'Dubai International Airport',
                'code' => 'DXB',
                'city' => 'Dubai',
                'country' => 'UAE',
                'timezone' => 'Asia/Dubai',
                'status' => 'publish'
            ],
            [
                'name' => 'Hamad International Airport',
                'code' => 'DOH',
                'city' => 'Doha',
                'country' => 'Qatar',
                'timezone' => 'Asia/Qatar',
                'status' => 'publish'
            ],
            [
                'name' => 'King Abdulaziz International Airport',
                'code' => 'JED',
                'city' => 'Jeddah',
                'country' => 'Saudi Arabia',
                'timezone' => 'Asia/Riyadh',
                'status' => 'publish'
            ],
            [
                'name' => 'King Khalid International Airport',
                'code' => 'RUH',
                'city' => 'Riyadh',
                'country' => 'Saudi Arabia',
                'timezone' => 'Asia/Riyadh',
                'status' => 'publish'
            ],
            [
                'name' => 'Abu Dhabi International Airport',
                'code' => 'AUH',
                'city' => 'Abu Dhabi',
                'country' => 'UAE',
                'timezone' => 'Asia/Dubai',
                'status' => 'publish'
            ],
            [
                'name' => 'Cairo International Airport',
                'code' => 'CAI',
                'city' => 'Cairo',
                'country' => 'Egypt',
                'timezone' => 'Africa/Cairo',
                'status' => 'publish'
            ],
            [
                'name' => 'London Heathrow Airport',
                'code' => 'LHR',
                'city' => 'London',
                'country' => 'United Kingdom',
                'timezone' => 'Europe/London',
                'status' => 'publish'
            ]
        ];

        foreach ($airports as $airport) {
            Airport::create(array_merge($airport, ['create_user' => 1]));
        }

        // Create sample flights
        $kuAirline = Airline::where('code', 'KU')->first();
        $emiratesAirline = Airline::where('code', 'EK')->first();
        $qatarAirline = Airline::where('code', 'QR')->first();

        $kwiAirport = Airport::where('code', 'KWI')->first();
        $dxbAirport = Airport::where('code', 'DXB')->first();
        $dohAirport = Airport::where('code', 'DOH')->first();
        $lhrAirport = Airport::where('code', 'LHR')->first();

        $flights = [
            [
                'title' => 'Kuwait to Dubai',
                'flight_number' => 'KU671',
                'airline_id' => $kuAirline->id,
                'airport_from' => $kwiAirport->id,
                'airport_to' => $dxbAirport->id,
                'departure_time' => Carbon::now()->addDays(7)->setTime(8, 30),
                'arrival_time' => Carbon::now()->addDays(7)->setTime(10, 0),
                'duration' => 90,
                'min_price' => 150.00,
                'aircraft_type' => 'Airbus A320',
                'status' => 'scheduled'
            ],
            [
                'title' => 'Dubai to Kuwait',
                'flight_number' => 'EK857',
                'airline_id' => $emiratesAirline->id,
                'airport_from' => $dxbAirport->id,
                'airport_to' => $kwiAirport->id,
                'departure_time' => Carbon::now()->addDays(8)->setTime(14, 15),
                'arrival_time' => Carbon::now()->addDays(8)->setTime(15, 45),
                'duration' => 90,
                'min_price' => 180.00,
                'aircraft_type' => 'Boeing 777',
                'status' => 'scheduled'
            ],
            [
                'title' => 'Kuwait to London',
                'flight_number' => 'KU101',
                'airline_id' => $kuAirline->id,
                'airport_from' => $kwiAirport->id,
                'airport_to' => $lhrAirport->id,
                'departure_time' => Carbon::now()->addDays(10)->setTime(2, 30),
                'arrival_time' => Carbon::now()->addDays(10)->setTime(7, 45),
                'duration' => 435,
                'min_price' => 450.00,
                'aircraft_type' => 'Boeing 777-300ER',
                'status' => 'scheduled'
            ],
            [
                'title' => 'Doha to Kuwait',
                'flight_number' => 'QR1087',
                'airline_id' => $qatarAirline->id,
                'airport_from' => $dohAirport->id,
                'airport_to' => $kwiAirport->id,
                'departure_time' => Carbon::now()->addDays(5)->setTime(16, 20),
                'arrival_time' => Carbon::now()->addDays(5)->setTime(17, 30),
                'duration' => 70,
                'min_price' => 120.00,
                'aircraft_type' => 'Airbus A350',
                'status' => 'scheduled'
            ]
        ];

        foreach ($flights as $flightData) {
            $flight = Flight::create(array_merge($flightData, [
                'code' => 'FL' . strtoupper(substr(uniqid(), -6)),
                'create_user' => 1
            ]));

            // Create seats for each flight
            $seatTypes = ['economy', 'business', 'first'];
            $seatCounts = ['economy' => 150, 'business' => 30, 'first' => 12];
            $basePrices = ['economy' => 1.0, 'business' => 2.5, 'first' => 5.0];

            foreach ($seatTypes as $seatType) {
                for ($i = 1; $i <= $seatCounts[$seatType]; $i++) {
                    FlightSeat::create([
                        'flight_id' => $flight->id,
                        'seat_type' => $seatType,
                        'seat_number' => $seatType[0] . $i,
                        'price' => $flight->min_price * $basePrices[$seatType],
                        'person' => 'adult',
                        'baggage_check_in' => $seatType === 'economy' ? 20 : ($seatType === 'business' ? 30 : 40),
                        'baggage_cabin' => $seatType === 'economy' ? 7 : 10,
                        'is_available' => true,
                        'create_user' => 1
                    ]);
                }
            }
        }
    }
}
