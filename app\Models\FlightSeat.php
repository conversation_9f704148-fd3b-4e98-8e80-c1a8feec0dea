<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FlightSeat extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'price',
        'max_passengers',
        'flight_id',
        'seat_type',
        'seat_number',
        'person',
        'baggage_check_in',
        'baggage_cabin',
        'is_available',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_available' => 'boolean'
    ];

    protected $appends = ['seat_class_text', 'person_type_text'];

    /**
     * Get seat class text
     */
    public function getSeatClassTextAttribute()
    {
        return match($this->seat_type) {
            'economy' => 'اقتصادية',
            'business' => 'رجال أعمال',
            'first' => 'درجة أولى',
            'premium_economy' => 'اقتصادية مميزة',
            default => 'غير محدد'
        };
    }

    /**
     * Get person type text
     */
    public function getPersonTypeTextAttribute()
    {
        return match($this->person) {
            'adult' => 'بالغ',
            'child' => 'طفل',
            'infant' => 'رضيع',
            default => 'غير محدد'
        };
    }

    /**
     * Get flight
     */
    public function flight()
    {
        return $this->belongsTo(Flight::class);
    }

    /**
     * Get seat type details
     */
    public function seatType()
    {
        return $this->belongsTo(SeatType::class, 'seat_type', 'code');
    }

    /**
     * Get booking passengers for this seat
     */
    public function passengers()
    {
        return $this->hasMany(BookingPassenger::class);
    }

    /**
     * Check if seat is bookable
     */
    public function isBookable()
    {
        return $this->is_available && $this->flight->status === 'scheduled';
    }

    /**
     * Reserve seat
     */
    public function reserve()
    {
        $this->update(['is_available' => false]);
    }

    /**
     * Release seat
     */
    public function release()
    {
        $this->update(['is_available' => true]);
    }

    /**
     * Scope for available seats
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope by seat type
     */
    public function scopeBySeatType($query, $seatType)
    {
        return $query->where('seat_type', $seatType);
    }

    /**
     * Scope by person type
     */
    public function scopeByPersonType($query, $personType)
    {
        return $query->where('person', $personType);
    }

    /**
     * Get creator user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    /**
     * Get updater user
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
