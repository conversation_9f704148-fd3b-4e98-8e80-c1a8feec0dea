<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

/**
 * Storage Helper for shared hosting compatibility
 * Handles image serving when symbolic links are not available
 */
class StorageHelper
{
    /**
     * Get the URL for a storage file
     * Works with or without symbolic links
     *
     * @param string $path
     * @return string
     */
    public static function url($path)
    {
        if (empty($path)) {
            return '';
        }

        // Check if symbolic link exists and works
        if (self::hasWorkingSymlink()) {
            return url(Storage::url($path));
        }

        // Use route-based file serving
        return route('storage.file', ['path' => $path]);
    }

    /**
     * Check if symbolic link exists and is working
     *
     * @return bool
     */
    public static function hasWorkingSymlink()
    {
        $linkPath = public_path('storage');
        $targetPath = storage_path('app/public');

        // Check if link exists
        if (!File::exists($linkPath)) {
            return false;
        }

        // Check if it's actually a symbolic link
        if (!is_link($linkPath)) {
            // It's a directory, check if it contains files from target
            $testFile = $targetPath . '/.gitkeep';
            $linkTestFile = $linkPath . '/.gitkeep';
            
            if (File::exists($testFile) && File::exists($linkTestFile)) {
                return true; // Directory copy is working
            }
            return false;
        }

        // It's a symbolic link, check if target is correct
        return realpath($linkPath) === realpath($targetPath);
    }

    /**
     * Create storage link using copy method if symlink fails
     *
     * @return bool
     */
    public static function createStorageLink()
    {
        $targetPath = storage_path('app/public');
        $linkPath = public_path('storage');

        // Remove existing link/directory
        if (File::exists($linkPath)) {
            if (is_link($linkPath)) {
                unlink($linkPath);
            } else {
                File::deleteDirectory($linkPath);
            }
        }

        // Try to create symbolic link first
        if (function_exists('symlink')) {
            try {
                if (symlink($targetPath, $linkPath)) {
                    return true;
                }
            } catch (\Exception $e) {
                // Symlink failed, continue to copy method
            }
        }

        // Fall back to copying directory
        return self::copyDirectory($targetPath, $linkPath);
    }

    /**
     * Copy directory recursively
     *
     * @param string $source
     * @param string $destination
     * @return bool
     */
    private static function copyDirectory($source, $destination)
    {
        try {
            if (!File::exists($source)) {
                File::makeDirectory($source, 0755, true);
            }

            if (!File::exists($destination)) {
                File::makeDirectory($destination, 0755, true);
            }

            // Copy all files and subdirectories
            $files = File::allFiles($source);
            foreach ($files as $file) {
                $relativePath = $file->getRelativePathname();
                $destFile = $destination . '/' . $relativePath;
                
                // Create directory if it doesn't exist
                $destDir = dirname($destFile);
                if (!File::exists($destDir)) {
                    File::makeDirectory($destDir, 0755, true);
                }
                
                File::copy($file->getPathname(), $destFile);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Sync files from storage to public (for copy method)
     * Call this after uploading new files
     *
     * @param string $relativePath
     * @return bool
     */
    public static function syncFile($relativePath)
    {
        if (self::hasWorkingSymlink() && is_link(public_path('storage'))) {
            return true; // No need to sync if symlink is working
        }

        $sourcePath = storage_path('app/public/' . $relativePath);
        $destPath = public_path('storage/' . $relativePath);

        if (!File::exists($sourcePath)) {
            return false;
        }

        // Create directory if it doesn't exist
        $destDir = dirname($destPath);
        if (!File::exists($destDir)) {
            File::makeDirectory($destDir, 0755, true);
        }

        return File::copy($sourcePath, $destPath);
    }

    /**
     * Get file path for direct serving
     *
     * @param string $path
     * @return string|null
     */
    public static function getFilePath($path)
    {
        $fullPath = storage_path('app/public/' . $path);
        
        if (File::exists($fullPath)) {
            return $fullPath;
        }
        
        return null;
    }

    /**
     * Get MIME type for a file
     *
     * @param string $path
     * @return string
     */
    public static function getMimeType($path)
    {
        $fullPath = self::getFilePath($path);
        
        if ($fullPath && File::exists($fullPath)) {
            return mime_content_type($fullPath) ?: 'application/octet-stream';
        }
        
        return 'application/octet-stream';
    }
}
