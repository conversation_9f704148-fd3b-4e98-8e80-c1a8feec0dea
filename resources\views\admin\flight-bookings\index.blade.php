@extends('layouts.admin')

@section('title', 'إدارة حجوزات الطيران')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-ticket-alt"></i>
                        حجوزات الطيران
                    </h3>
                    <div>
                        <button class="btn btn-success" onclick="exportBookings()">
                            <i class="fas fa-download"></i>
                            تصدير Excel
                        </button>
                        <button class="btn btn-info" data-toggle="modal" data-target="#statsModal">
                            <i class="fas fa-chart-bar"></i>
                            الإحصائيات
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- إحصائيات سريعة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-ticket-alt"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي الحجوزات</span>
                                    <span class="info-box-number">{{ $totalBookings }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">مؤكدة</span>
                                    <span class="info-box-number">{{ $confirmedBookings }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-clock"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">في الانتظار</span>
                                    <span class="info-box-number">{{ $pendingBookings }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">ملغية</span>
                                    <span class="info-box-number">{{ $cancelledBookings }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="row mb-3">
                        <div class="col-md-2">
                            <input type="text" class="form-control" id="booking-reference-filter" 
                                   placeholder="رقم الحجز">
                        </div>
                        <div class="col-md-2">
                            <select class="form-control" id="status-filter">
                                <option value="">جميع الحالات</option>
                                <option value="pending">في الانتظار</option>
                                <option value="confirmed">مؤكد</option>
                                <option value="cancelled">ملغي</option>
                                <option value="completed">مكتمل</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-control" id="payment-status-filter">
                                <option value="">جميع حالات الدفع</option>
                                <option value="pending">في الانتظار</option>
                                <option value="paid">مدفوع</option>
                                <option value="failed">فشل</option>
                                <option value="refunded">مسترد</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="date-from-filter" placeholder="من تاريخ">
                        </div>
                        <div class="col-md-2">
                            <input type="date" class="form-control" id="date-to-filter" placeholder="إلى تاريخ">
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary btn-block" onclick="filterBookings()">
                                <i class="fas fa-search"></i>
                                بحث
                            </button>
                        </div>
                    </div>

                    <!-- جدول الحجوزات -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="bookings-table">
                            <thead>
                                <tr>
                                    <th>رقم الحجز</th>
                                    <th>معلومات الرحلة</th>
                                    <th>المسافر</th>
                                    <th>المقاعد</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>حالة الحجز</th>
                                    <th>حالة الدفع</th>
                                    <th>تاريخ الحجز</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($bookings as $booking)
                                <tr>
                                    <td>
                                        <strong>{{ $booking->booking_reference }}</strong>
                                        @if($booking->is_group_booking)
                                            <span class="badge badge-info">جماعي</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $booking->flight->flight_number }}</strong>
                                            <small class="d-block text-muted">{{ $booking->flight->airline->name }}</small>
                                        </div>
                                        <div>
                                            <small>
                                                {{ $booking->flight->fromAirport->code }} → {{ $booking->flight->toAirport->code }}
                                            </small>
                                        </div>
                                        <div>
                                            <small class="text-muted">
                                                {{ $booking->flight->departure_date->format('Y-m-d') }} 
                                                {{ $booking->flight->departure_time }}
                                            </small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $booking->passenger_name }}</strong>
                                        </div>
                                        <div>
                                            <small class="text-muted">{{ $booking->passenger_email }}</small>
                                        </div>
                                        <div>
                                            <small class="text-muted">{{ $booking->passenger_phone }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-primary">
                                            {{ $booking->passengers_count }} مسافر
                                        </span>
                                        @if($booking->seats->count() > 0)
                                            <div class="mt-1">
                                                @foreach($booking->seats as $seat)
                                                    <small class="badge badge-secondary">{{ $seat->seat_number }}</small>
                                                @endforeach
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ number_format($booking->total_price, 3) }} د.ك</strong>
                                        @if($booking->discount_amount > 0)
                                            <div>
                                                <small class="text-success">
                                                    خصم: {{ number_format($booking->discount_amount, 3) }} د.ك
                                                </small>
                                            </div>
                                        @endif
                                    </td>
                                    <td>
                                        @switch($booking->status)
                                            @case('pending')
                                                <span class="badge badge-warning">في الانتظار</span>
                                                @break
                                            @case('confirmed')
                                                <span class="badge badge-success">مؤكد</span>
                                                @break
                                            @case('cancelled')
                                                <span class="badge badge-danger">ملغي</span>
                                                @break
                                            @case('completed')
                                                <span class="badge badge-info">مكتمل</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        @switch($booking->payment_status)
                                            @case('pending')
                                                <span class="badge badge-secondary">في الانتظار</span>
                                                @break
                                            @case('paid')
                                                <span class="badge badge-success">مدفوع</span>
                                                @break
                                            @case('failed')
                                                <span class="badge badge-danger">فشل</span>
                                                @break
                                            @case('refunded')
                                                <span class="badge badge-warning">مسترد</span>
                                                @break
                                        @endswitch
                                    </td>
                                    <td>
                                        <div>{{ $booking->created_at->format('Y-m-d') }}</div>
                                        <small class="text-muted">{{ $booking->created_at->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.flight-bookings.show', $booking->id) }}" 
                                               class="btn btn-sm btn-info" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($booking->status == 'pending')
                                                <button class="btn btn-sm btn-success" 
                                                        onclick="confirmBooking({{ $booking->id }})" title="تأكيد">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            @endif
                                            @if(in_array($booking->status, ['pending', 'confirmed']))
                                                <button class="btn btn-sm btn-danger" 
                                                        onclick="cancelBooking({{ $booking->id }})" title="إلغاء">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            @endif
                                            <button class="btn btn-sm btn-primary" 
                                                    onclick="printTicket({{ $booking->id }})" title="طباعة التذكرة">
                                                <i class="fas fa-print"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $bookings->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Modal -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إحصائيات الحجوزات</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <canvas id="bookingStatusChart"></canvas>
                    </div>
                    <div class="col-md-6">
                        <canvas id="monthlyBookingsChart"></canvas>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6>أفضل الوجهات</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الوجهة</th>
                                        <th>عدد الحجوزات</th>
                                        <th>إجمالي الإيرادات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($topDestinations as $destination)
                                    <tr>
                                        <td>{{ $destination->to_airport_name }}</td>
                                        <td>{{ $destination->bookings_count }}</td>
                                        <td>{{ number_format($destination->total_revenue, 3) }} د.ك</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#bookings-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 7, "desc" ]],
        "pageLength": 25
    });

    // Initialize charts when modal is shown
    $('#statsModal').on('shown.bs.modal', function() {
        initializeCharts();
    });
});

function filterBookings() {
    const bookingRef = $('#booking-reference-filter').val();
    const status = $('#status-filter').val();
    const paymentStatus = $('#payment-status-filter').val();
    const dateFrom = $('#date-from-filter').val();
    const dateTo = $('#date-to-filter').val();
    
    const params = new URLSearchParams();
    if (bookingRef) params.append('booking_reference', bookingRef);
    if (status) params.append('status', status);
    if (paymentStatus) params.append('payment_status', paymentStatus);
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    
    window.location.href = '{{ route("admin.flight-bookings.index") }}?' + params.toString();
}

function confirmBooking(id) {
    if (confirm('هل تريد تأكيد هذا الحجز؟')) {
        $.ajax({
            url: '/admin/flight-bookings/' + id + '/confirm',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + response.message);
                }
            }
        });
    }
}

function cancelBooking(id) {
    const reason = prompt('سبب الإلغاء (اختياري):');
    if (reason !== null) {
        $.ajax({
            url: '/admin/flight-bookings/' + id + '/cancel',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                reason: reason
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + response.message);
                }
            }
        });
    }
}

function printTicket(id) {
    window.open('/admin/flight-bookings/' + id + '/ticket', '_blank');
}

function exportBookings() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '{{ route("admin.flight-bookings.export") }}?' + params.toString();
}

function initializeCharts() {
    // Booking Status Chart
    const statusCtx = document.getElementById('bookingStatusChart').getContext('2d');
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: ['مؤكد', 'في الانتظار', 'ملغي', 'مكتمل'],
            datasets: [{
                data: [{{ $confirmedBookings }}, {{ $pendingBookings }}, {{ $cancelledBookings }}, {{ $completedBookings ?? 0 }}],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#17a2b8']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع حالات الحجز'
                }
            }
        }
    });

    // Monthly Bookings Chart
    const monthlyCtx = document.getElementById('monthlyBookingsChart').getContext('2d');
    new Chart(monthlyCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($monthlyLabels ?? []) !!},
            datasets: [{
                label: 'عدد الحجوزات',
                data: {!! json_encode($monthlyData ?? []) !!},
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: 'الحجوزات الشهرية'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}
</script>
@endsection
