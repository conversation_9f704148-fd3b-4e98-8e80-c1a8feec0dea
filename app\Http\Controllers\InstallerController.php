<?php

namespace App\Http\Controllers;

use dacoto\EnvSet\Facades\EnvSet;
use dacoto\LaravelWizardInstaller\Controllers\InstallFolderController;
use dacoto\LaravelWizardInstaller\Controllers\InstallServerController;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class InstallerController extends Controller {
    public function purchaseCodeIndex() {
        // Skip purchase code verification - redirect directly to next step
        return redirect()->route('install.php-function.index');
    }


    public function checkPurchaseCode(Request $request) {
        // Skip purchase code verification - redirect directly to next step
        return redirect()->route('install.php-function.index');
    }

    public function phpFunctionIndex() {
        if (!(new InstallServerController())->check() || !(new InstallFolderController())->check()) {
            return redirect()->route('install.folders');
        }
        return view('vendor.installer.steps.symlink_basedir_check', [
            'result' => $this->checkSymlink(),
            'baseDir' =>$this->checkBaseDir()
        ]);
    }

    public function checkSymlink(): bool
    {
        // Skip symlink check for shared hosting compatibility
        // Most shared hosting providers disable symlink for security
        return true; // Always return true to bypass this check
    }
    public function checkBaseDir(): bool
    {
        $openBaseDir = ini_get('open_basedir');
        // Return true if open_basedir is disabled (empty) - this is what we want
        return empty($openBaseDir);
    }

}
