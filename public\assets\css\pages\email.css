.media {
    align-items: center;
    display: flex
}

.media .media-body {
    flex: 1;
    padding-left: 1rem
}

.email-application .content-area-wrapper {
    border: 1px solid #dfe3e7;
    border-radius: .267rem;
    display: flex;
    height: calc(100% - 5rem);
    position: relative
}

.email-application .content-area-wrapper .app-content-overlay {
    border-radius: .267rem;
    inset: 0;
    opacity: 0;
    position: absolute;
    visibility: hidden;
    z-index: 7
}

.email-application .content-area-wrapper .app-content-overlay.show {
    background-color: rgba(0, 0, 0, .2);
    opacity: 1;
    transition: all .3s ease;
    visibility: visible
}

.email-application .content-area-wrapper .sidebar {
    position: inherit
}

.email-application .content-area-wrapper .sidebar.show .email-app-sidebar {
    transform: translateX(13%) translateY(-1px)
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar {
    border-bottom-left-radius: .267rem;
    border-right: 1px solid #dfe3e7;
    border-top-left-radius: .267rem;
    height: calc(100vh - 9rem);
    transition: all .3s ease;
    width: 300px
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu {
    background-color: #fff;
    width: 100%;
    z-index: 3
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu .form-group-compose {
    margin-bottom: .8rem;
    padding: 1px 1.5rem
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu .form-group-compose .compose-btn {
    box-shadow: 0 2px 4px 0 rgba(90, 141, 238, .6);
    font-weight: 500
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu .sidebar-menu-list {
    height: calc(100% - 6.4rem);
    padding: 0 1.7rem;
    position: relative
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu .sidebar-menu-list a:hover {
    color: #596f88
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu .list-group .list-group-item {
    background-color: transparent;
    border: none;
    font-weight: 500;
    padding: .6rem 0
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu .list-group .list-group-item.active {
    color: #5a8dee
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .email-app-menu .list-group .list-group-item.active:before {
    background-color: #5a8dee;
    content: "";
    height: 30px;
    left: -26px;
    position: absolute;
    width: 2px
}

.email-application .content-area-wrapper .sidebar .email-app-sidebar .sidebar-close-icon {
    cursor: pointer;
    font-size: 1.25rem;
    position: absolute;
    right: .25rem;
    top: .25rem;
    visibility: hidden;
    z-index: 5
}

@media screen and (max-width:992px) {
    .email-application .content-area-wrapper .sidebar .email-app-sidebar {
        background-color: #fafbfb;
        height: calc(100vh - 8.98rem);
        left: 0;
        position: absolute;
        top: 1px;
        transform: translateX(-120%);
        transition: transform .25s;
        z-index: 8
    }

    .email-application .content-area-wrapper .sidebar .email-app-sidebar.show {
        transform: translateX(0)
    }

    .email-application .content-area-wrapper .sidebar .email-app-sidebar .sidebar-close-icon {
        visibility: visible
    }
}

.email-application .content-area-wrapper .sidebar .compose-new-mail-sidebar {
    background-color: #fff;
    border-radius: 0 .267rem .267rem 0;
    bottom: 1px;
    height: calc(100vh - 9rem);
    position: absolute;
    right: 2.15rem;
    transform: translateX(130%);
    transition: all .3s ease;
    width: 400px;
    z-index: 8
}

.email-application .content-area-wrapper .sidebar .compose-new-mail-sidebar.show {
    transform: translateX(8%) translateY(1px)
}

.email-application .content-area-wrapper .sidebar .compose-new-mail-sidebar .card-footer .btn-send {
    box-shadow: 0 2px 4px 0 rgba(90, 141, 238, .6)
}

.email-application .content-area-wrapper .sidebar .compose-new-mail-sidebar .close-icon {
    outline: 0;
    position: absolute;
    right: 15px;
    top: 18px
}

.email-application .content-area-wrapper .sidebar .compose-new-mail-sidebar .close-icon i {
    font-size: 1.75rem
}

@media screen and (max-width:992px) {
    .email-application .content-area-wrapper .sidebar .compose-new-mail-sidebar.show {
        transform: translateX(10.5%) translateY(1px)
    }
}

.email-application .content-area-wrapper .content-right {
    background-color: #fff;
    width: calc(100% - 260px)
}

.email-application .content-area-wrapper .content-right .content-wrapper {
    padding: 0
}

.email-application .content-area-wrapper .content-right .selected-row-bg {
    background-color: #e7edf3 !important
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .checkbox,
.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .checkbox label {
    cursor: pointer
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action {
    border-bottom: 1px solid #dfe3e7;
    display: flex;
    padding: 1rem 1.5rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-right .email-fixed-search {
    margin: 0 .86rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-right .email-fixed-search .sidebar-toggle {
    cursor: pointer;
    float: left;
    line-height: 1.1;
    margin: .5rem .99rem .5rem 0
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-right .email-fixed-search input {
    font-family: IBM Plex Sans, Helvetica, Arial, serif
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .email-action .action-left ul li i {
    top: 0
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-right .email-pagination-next,
.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-right .email-pagination-prev {
    margin-left: .6rem;
    padding: .35rem .5rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-left ul li .dropdown-toggle:after {
    display: none
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-left ul li .dropdown-menu .dropdown-item .bullet,
.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-left ul li .dropdown-menu .dropdown-item i {
    margin-right: .5rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action .action-left .list-inline-item:not(:last-child) {
    margin-right: .86rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list {
    height: calc(100vh - 13.65rem);
    position: relative
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .ps__rail-y {
    z-index: 6
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper {
    margin: 0;
    padding: 0
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li {
    -webkit-animation: fadeIn .5s linear;
    animation: fadeIn .5s linear;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    cursor: pointer;
    position: relative
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li.media {
    align-items: center;
    background-color: #fff;
    padding: 1rem 1.5rem;
    z-index: 1
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li.media:hover {
    box-shadow: 0 0 10px 0 rgba(58, 70, 93, .25);
    transform: translateY(1px);
    transition: all .2s;
    z-index: 5
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li.media .media-body {
    overflow: hidden
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .avatar,
.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .avatar img {
    height: 38px;
    width: 38px
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .avatar img {
    border: 2px solid #fff
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li.mail-read {
    background-color: #f2f4f4
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li.mail-read .list-group-item-text {
    font-weight: 400 !important
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li:not(:first-child) {
    border-top: 1px solid #dfe3e7
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .user-details {
    display: flex;
    justify-content: space-between
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .user-details .list-group-item-text {
    font-size: 1rem;
    font-weight: 500
}

@media (max-width:575.98px) {
    .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .user-details .mail-items {
        display: inline-grid;
        width: 70%
    }

    .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .user-details .mail-meta-item {
        position: absolute;
        right: 1rem
    }

    .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .user-details .mail-meta-item .mail-date {
        margin: 1rem .75rem 0 0
    }
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .mail-date {
    color: #828d99;
    font-size: .86rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .mail-message {
    display: flex;
    justify-content: space-between;
    text-overflow: ellipsis;
    white-space: nowrap
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li .mail-message p {
    color: #828d99;
    font-size: .8rem;
    line-height: 1.75;
    margin-right: 1rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper .user-action {
    align-items: center;
    display: flex
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper .user-action .favorite {
    color: #c7cfd6;
    margin-right: .5rem
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper .user-action .favorite i {
    font-size: 1.3rem;
    line-height: 1.5
}

@media screen and (max-width:560px) {
    .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper .user-action .favorite {
        display: none
    }
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .no-results {
    display: none;
    padding: 1.5rem;
    text-align: center
}

.email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .no-results.show {
    display: block
}

@media screen and (max-width:992px) {
    .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list {
        height: calc(100vh - 17.4rem) !important
    }
}

.email-application .content-area-wrapper .content-right .email-app-details {
    background-color: #f2f4f4;
    display: block;
    height: 100%;
    opacity: 0;
    position: absolute;
    top: 0;
    transform: translateX(100%);
    transition: all .3s ease;
    visibility: hidden;
    width: calc(100% - 260px);
    z-index: 6
}

.email-application .content-area-wrapper .content-right .email-app-details.show {
    border-left: 1px solid #dfe3e7;
    opacity: 1;
    overflow: hidden;
    transform: translateX(0);
    visibility: visible
}

.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header {
    background-color: #fff;
    border-bottom: 1px solid #dfe3e7;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: .85rem 1.5rem 0
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area .email-detail-head .collapse-header .card-header .information .dropdown-menu .dropdown-item {
    margin-right: .5rem
}

.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .dropdown-menu .dropdown-item .bullet,
.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .dropdown-menu .dropdown-item i,
.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .email-header-right .dropdown-toggle:after,
.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .email-scroll-area .email-detail-head .collapse-header .card-header .information .dropdown-toggle:after {
    display: none
}

.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .go-back {
    cursor: pointer
}

.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .email-pagination-next i,
.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .email-pagination-prev i {
    top: 2px
}

.email-application .content-area-wrapper .content-right .email-app-details .email-detail-header .email-detail-title {
    color: #475f7b;
    font-size: 1.2rem;
    position: relative;
    top: 2px
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area {
    height: calc(100vh - 13.5rem);
    padding: 0 1rem;
    position: relative
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area .email-detail-head {
    padding: 1.5rem .5rem .75rem
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area .email-detail-head .collapse-header {
    background-color: transparent;
    margin-bottom: 1.2rem
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area .email-detail-head .collapse-header.open {
    background-color: #fff !important;
    box-shadow: -8px 12px 18px 0 rgba(25, 42, 70, .13) !important
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area .email-detail-head .collapse-header .card-header {
    border-color: #dfe3e7;
    font-size: 1rem
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area .email-detail-head .collapse-header .card-header .information .dropdown-toggle {
    color: #727e8c
}

.email-application .content-area-wrapper .content-right .email-app-details .email-scroll-area .email-detail-head .collapse-header .card-header .information .dropdown-menu {
    transform: translate3d(-144px, 19px, 0) !important
}

@media screen and (max-width:992px) {
    .email-application .content-area-wrapper .content-right {
        width: 100%
    }

    .email-application .content-area-wrapper .content-right .email-action {
        flex-direction: column-reverse
    }

    .email-application .content-area-wrapper .content-right .email-action .action-right {
        margin-bottom: 1rem
    }

    .email-application .content-area-wrapper .content-right .email-action .action-right .email-fixed-search {
        margin-left: 0 !important
    }

    .email-application .content-area-wrapper .content-right .email-action .action-right .sidebar-toggle {
        margin-right: .3rem
    }

    .email-application .content-area-wrapper .content-right .email-app-details {
        border-radius: .267rem;
        width: 100%
    }
}

.email-application .content-area-wrapper .sidebar-label {
    color: #828d99;
    font-family: Rubik, Helvetica, Arial, serif;
    letter-spacing: 1px;
    margin: 1.8rem 0 .5rem
}

.email-application .content-area-wrapper .attchement-text {
    font-family: IBM Plex Sans, Helvetica, Arial, serif
}

.email-application .content-area-wrapper .action-icon {
    border: 1px solid #dfe3e7;
    padding: .35rem .5rem
}

.email-application .content-area-wrapper .quill-wrapper {
    padding: 1.3rem 1.7rem
}

.email-application .content-area-wrapper .quill-wrapper .snow-container {
    position: relative
}

.email-application .content-area-wrapper .quill-wrapper .snow-container .ql-snow .ql-tooltip {
    left: 0 !important
}

.email-application .content-area-wrapper .quill-wrapper .snow-container .ql-container.ql-snow,
.email-application .content-area-wrapper .quill-wrapper .snow-container .ql-toolbar {
    border: none
}

.email-application .content-area-wrapper .quill-wrapper .snow-container .send-btn {
    box-shadow: 0 2px 4px 0 rgba(90, 141, 238, .6);
    font-weight: 500;
    padding: .2rem 1rem
}

.email-application .content-area-wrapper .quill-wrapper .ql-editor.ql-blank:before {
    left: 0
}

.email-application .content-area-wrapper .quill-wrapper .ql-editor {
    min-height: 80px;
    padding-left: 0;
    padding-right: 0
}

@media screen and (max-width:1280px) {
    .content-right .email-app-list-wrapper .email-app-list .email-user-list {
        height: calc(100vh - 13.9rem)
    }
}

@media (max-width:767.98px) {
    .compose-new-mail-sidebar {
        width: auto
    }
}

@media screen and (max-width:436px) {
    .email-app-details .email-header-right {
        margin-left: 0 !important;
        padding-left: 0 !important
    }

    .email-app-details .email-header-right li {
        margin-right: 0
    }
}

@-webkit-keyframes fadeIn {
    0% {
        opacity: 0;
        top: 100px
    }

    75% {
        opacity: .5;
        top: 0
    }

    to {
        opacity: 1
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        top: 100px
    }

    75% {
        opacity: .5;
        top: 0
    }

    to {
        opacity: 1
    }
}

body.theme-dark .email-application .content-area-wrapper {
    border: 1px solid #2a2f3e
}

body.theme-dark .email-application .content-area-wrapper .email-app-sidebar {
    border-right: 1px solid #343a40
}

body.theme-dark .email-application .content-area-wrapper .email-app-sidebar .email-app-menu {
    background-color: #1e1e2d
}

body.theme-dark .email-application .content-area-wrapper .email-app-sidebar .email-app-menu .list-group .list-group-item {
    color: #9899ac
}

body.theme-dark .email-application .content-area-wrapper .email-app-sidebar .email-app-menu .list-group .list-group-item:hover {
    color: #187de4
}

body.theme-dark .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list {
    overflow-y: scroll !important
}

body.theme-dark .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li.media {
    background-color: #1e1e2d
}

body.theme-dark .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li.mail-read {
    background-color: #2b2b41
}

body.theme-dark .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-user-list .users-list-wrapper li:not(:first-child) {
    border-top: 1px solid #495057
}

body.theme-dark .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action {
    background: #151521
}

body.theme-dark .email-application .content-area-wrapper .action-button,
body.theme-dark .email-application .content-area-wrapper .action-icon,
body.theme-dark .email-application .content-area-wrapper .form-control {
    border: 1px solid #373752
}

body.theme-dark body.theme-dark .form-check-input {
    background-color: #151521;
    border: 2px solid #087C7C
}

body.theme-dark .email-application .content-body {
    background-color: #087C7C
}

body.theme-dark .email-application .content-area-wrapper .content-right .email-app-list-wrapper .email-app-list .email-action {
    border-bottom: 1px solid #087C7C
}
