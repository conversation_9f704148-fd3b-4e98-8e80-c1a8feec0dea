<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // تعطيل نظام الموقع الجغرافي نهائياً
        
        // إزالة الحقول الجغرافية من جدول items
        if (Schema::hasColumn('items', 'latitude')) {
            Schema::table('items', function (Blueprint $table) {
                $table->dropColumn(['latitude', 'longitude', 'address']);
            });
        }
        
        // تحديث إعدادات النظام لتعطيل الموقع
        DB::table('settings')->updateOrInsert(
            ['name' => 'enable_location_system'],
            ['value' => '0', 'type' => 'string']
        );
        
        DB::table('settings')->updateOrInsert(
            ['name' => 'enable_maps'],
            ['value' => '0', 'type' => 'string']
        );
        
        DB::table('settings')->updateOrInsert(
            ['name' => 'require_location'],
            ['value' => '0', 'type' => 'string']
        );
        
        DB::table('settings')->updateOrInsert(
            ['name' => 'enable_location_filter'],
            ['value' => '0', 'type' => 'string']
        );
        
        // تعطيل Google Maps
        DB::table('settings')->updateOrInsert(
            ['name' => 'google_maps_enabled'],
            ['value' => '0', 'type' => 'string']
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة تفعيل نظام الموقع
        Schema::table('items', function (Blueprint $table) {
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            $table->text('address')->nullable();
        });
        
        DB::table('settings')->updateOrInsert(
            ['name' => 'enable_location_system'],
            ['value' => '1', 'type' => 'string']
        );
        
        DB::table('settings')->updateOrInsert(
            ['name' => 'enable_maps'],
            ['value' => '1', 'type' => 'string']
        );
    }
};
