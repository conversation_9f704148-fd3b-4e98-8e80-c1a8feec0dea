<?php
/**
 * eClassify Travel Offers - Password Reset Tool
 * Reset password for any user in the system
 * 
 * <AUTHOR>
 * @project eClassify Travel Offers
 */

echo "<h1>🔑 eClassify Travel Offers - Password Reset Tool</h1>";

if ($_POST) {
    $email = $_POST['email'] ?? '';
    $new_password = $_POST['password'] ?? '';
    
    if ($email && $new_password) {
        try {
            $pdo = new PDO(
                'mysql:host=localhost;dbname=ebroker_clsfd',
                'root',
                '',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
            $result = $stmt->execute([$hashed_password, $email]);
            
            if ($result && $stmt->rowCount() > 0) {
                echo "<div style='background: #dcfce7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h2>✅ Password Reset Successful!</h2>";
                echo "<p><strong>Email:</strong> $email</p>";
                echo "<p><strong>New Password:</strong> $new_password</p>";
                echo "<p><a href='/' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Login Now</a></p>";
                echo "</div>";
            } else {
                echo "<div style='background: #fef2f2; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h2>❌ User Not Found</h2>";
                echo "<p>No user found with email: $email</p>";
                echo "</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div style='background: #fef2f2; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
            echo "<h2>❌ Database Error</h2>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
}

echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔧 Reset User Password</h2>";
echo "<form method='post' style='background: white; padding: 20px; border-radius: 8px; border: 1px solid #ddd;'>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label for='email' style='display: block; margin-bottom: 5px; font-weight: bold;'>User Email:</label>";
echo "<input type='email' name='email' id='email' value='<EMAIL>' required style='width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
echo "</div>";
echo "<div style='margin-bottom: 15px;'>";
echo "<label for='password' style='display: block; margin-bottom: 5px; font-weight: bold;'>New Password:</label>";
echo "<input type='text' name='password' id='password' value='admin123456' required style='width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
echo "</div>";
echo "<button type='submit' style='background: #059669; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Reset Password</button>";
echo "</form>";
echo "</div>";

// Show existing users
try {
    $pdo = new PDO(
        'mysql:host=localhost;dbname=ebroker_clsfd',
        'root',
        '',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $stmt = $pdo->query("SELECT id, name, email, created_at FROM users ORDER BY id ASC LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($users) {
        echo "<div style='background: #e0f2fe; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
        echo "<h2>👥 Existing Users</h2>";
        echo "<table style='width: 100%; border-collapse: collapse;'>";
        echo "<tr style='background: #f3f4f6;'>";
        echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>ID</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Name</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Email</th>";
        echo "<th style='padding: 10px; border: 1px solid #ddd; text-align: left;'>Created</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $user['id'] . "</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>⚠️ Database Connection</h2>";
    echo "<p>Could not connect to database to show users.</p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📋 Common Admin Credentials</h2>";
echo "<p><strong>Default Admin:</strong></p>";
echo "<ul>";
echo "<li>Email: <EMAIL></li>";
echo "<li>Password: admin123456</li>";
echo "</ul>";
echo "<p><strong>Alternative Admin:</strong></p>";
echo "<ul>";
echo "<li>Email: <EMAIL></li>";
echo "<li>Password: 12345678</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<p><strong>eClassify Travel Offers</strong> - Password Management</p>";
echo "<p>Developed by AmrDev | Secure Access Control</p>";
echo "</div>";
?>
