<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SeatType extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'code',
        'name',
        'name_ar',
        'description',
        'features',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'features' => 'array'
    ];

    public function flightSeats()
    {
        return $this->hasMany(FlightSeat::class, 'seat_type', 'code');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
