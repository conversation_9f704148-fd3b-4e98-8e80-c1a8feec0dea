<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TicketBooking;
use App\Models\Event;
use App\Models\IndividualTicket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TicketBookingController extends Controller
{
    /**
     * Display a listing of ticket bookings
     */
    public function index(Request $request)
    {
        $query = TicketBooking::with(['event', 'tickets.ticketCategory']);

        // Apply filters
        if ($request->filled('booking_reference')) {
            $query->where('booking_reference', 'like', '%' . $request->booking_reference . '%');
        }

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $bookings = $query->latest()->paginate(25);
        $events = Event::active()->get();

        // Statistics
        $totalBookings = TicketBooking::count();
        $confirmedBookings = TicketBooking::where('status', 'confirmed')->count();
        $pendingBookings = TicketBooking::where('status', 'pending')->count();
        $cancelledBookings = TicketBooking::where('status', 'cancelled')->count();

        // Top events
        $topEvents = TicketBooking::select(
                'event_id',
                DB::raw('COUNT(*) as bookings_count'),
                DB::raw('SUM(total_amount) as total_revenue')
            )
            ->with('event')
            ->where('status', '!=', 'cancelled')
            ->groupBy('event_id')
            ->orderBy('bookings_count', 'desc')
            ->limit(5)
            ->get();

        // Monthly data for charts
        $monthlyData = TicketBooking::select(
                DB::raw('MONTH(created_at) as month'),
                DB::raw('COUNT(*) as count')
            )
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('count', 'month')
            ->toArray();

        $monthlyLabels = [];
        $monthlyBookings = [];
        for ($i = 1; $i <= 12; $i++) {
            $monthlyLabels[] = date('M', mktime(0, 0, 0, $i, 1));
            $monthlyBookings[] = $monthlyData[$i] ?? 0;
        }

        return view('admin.ticket-bookings.index', compact(
            'bookings', 'events', 'totalBookings', 'confirmedBookings', 
            'pendingBookings', 'cancelledBookings', 'topEvents',
            'monthlyLabels', 'monthlyData'
        ));
    }

    /**
     * Display the specified booking
     */
    public function show(TicketBooking $ticketBooking)
    {
        $ticketBooking->load(['event', 'tickets.ticketCategory']);

        return view('admin.ticket-bookings.show', compact('ticketBooking'));
    }

    /**
     * Confirm a booking
     */
    public function confirm(TicketBooking $ticketBooking)
    {
        try {
            $ticketBooking->update([
                'status' => 'confirmed',
                'confirmed_at' => now(),
                'confirmed_by' => auth()->id(),
            ]);

            // Update ticket status
            $ticketBooking->tickets()->update(['status' => 'sold']);

            // Send confirmation email/SMS here

            return response()->json([
                'success' => true,
                'message' => 'تم تأكيد الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تأكيد الحجز: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Cancel a booking
     */
    public function cancel(Request $request, TicketBooking $ticketBooking)
    {
        try {
            $ticketBooking->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
                'cancelled_by' => auth()->id(),
                'cancellation_reason' => $request->reason,
            ]);

            // Free up the tickets
            $ticketBooking->tickets()->update(['status' => 'available']);

            // Process refund if payment was made
            if ($ticketBooking->payment_status === 'paid') {
                // Process refund logic here
                $ticketBooking->update(['payment_status' => 'refunded']);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء الحجز: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Generate tickets PDF
     */
    public function tickets(TicketBooking $ticketBooking)
    {
        $ticketBooking->load(['event', 'tickets.ticketCategory']);

        // Generate PDF tickets here
        // This would require a PDF library like DomPDF or TCPDF

        return view('admin.ticket-bookings.tickets', compact('ticketBooking'));
    }

    /**
     * Export bookings to Excel
     */
    public function export(Request $request)
    {
        $query = TicketBooking::with(['event', 'tickets.ticketCategory']);

        // Apply same filters as index
        if ($request->filled('booking_reference')) {
            $query->where('booking_reference', 'like', '%' . $request->booking_reference . '%');
        }

        if ($request->filled('event_id')) {
            $query->where('event_id', $request->event_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $bookings = $query->get();

        // Export to Excel logic here
        // This would require Laravel Excel package

        $filename = 'ticket_bookings_' . date('Y-m-d_H-i-s') . '.xlsx';
        
        return response()->json([
            'success' => true,
            'message' => 'تم تصدير البيانات بنجاح',
            'download_url' => route('admin.ticket-bookings.download', ['file' => $filename])
        ]);
    }

    /**
     * Validate a ticket
     */
    public function validateTicket(IndividualTicket $ticket)
    {
        try {
            if ($ticket->status !== 'sold') {
                return response()->json([
                    'success' => false,
                    'message' => 'هذه التذكرة غير صالحة للاستخدام'
                ]);
            }

            $ticket->update([
                'status' => 'used',
                'used_at' => now(),
                'validated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تأكيد استخدام التذكرة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Print individual ticket
     */
    public function printTicket(IndividualTicket $ticket)
    {
        $ticket->load(['ticketCategory.event', 'booking']);

        // Generate PDF ticket here
        return view('admin.tickets.print', compact('ticket'));
    }

    /**
     * Update booking status
     */
    public function updateStatus(Request $request, TicketBooking $ticketBooking)
    {
        $request->validate([
            'status' => 'required|in:pending,confirmed,cancelled,completed',
        ]);

        try {
            $ticketBooking->update([
                'status' => $request->status,
                'updated_by' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث حالة الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحديث: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Send booking confirmation
     */
    public function sendConfirmation(TicketBooking $ticketBooking)
    {
        try {
            // Send confirmation email/SMS logic here
            
            return response()->json([
                'success' => true,
                'message' => 'تم إرسال تأكيد الحجز بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء الإرسال: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get booking statistics
     */
    public function statistics()
    {
        $stats = [
            'total_bookings' => TicketBooking::count(),
            'confirmed_bookings' => TicketBooking::where('status', 'confirmed')->count(),
            'pending_bookings' => TicketBooking::where('status', 'pending')->count(),
            'cancelled_bookings' => TicketBooking::where('status', 'cancelled')->count(),
            'total_revenue' => TicketBooking::where('payment_status', 'paid')->sum('total_amount'),
            'today_bookings' => TicketBooking::whereDate('created_at', today())->count(),
            'this_month_bookings' => TicketBooking::whereMonth('created_at', date('m'))->count(),
        ];

        return response()->json($stats);
    }
}
