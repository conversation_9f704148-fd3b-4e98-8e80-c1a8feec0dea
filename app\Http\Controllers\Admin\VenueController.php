<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Venue;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VenueController extends Controller
{
    /**
     * Display a listing of venues
     */
    public function index(Request $request)
    {
        $query = Venue::query();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('name_ar', 'like', "%{$search}%")
                  ->orWhere('city', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('city')) {
            $query->where('city', $request->city);
        }

        if ($request->filled('country')) {
            $query->where('country', $request->country);
        }

        $venues = $query->orderBy('name')->paginate(20);

        return view('admin.venues.index', compact('venues'));
    }

    /**
     * Show the form for creating a new venue
     */
    public function create()
    {
        return view('admin.venues.create');
    }

    /**
     * Store a newly created venue
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'capacity' => 'required|integer|min:1',
            'map_lat' => 'nullable|numeric',
            'map_lng' => 'nullable|numeric',
            'facilities' => 'nullable|array',
            'status' => 'required|in:active,inactive'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $data = $request->only([
            'name', 'name_ar', 'address', 'city', 'country', 
            'capacity', 'map_lat', 'map_lng', 'status'
        ]);
        
        $data['facilities'] = $request->facilities ?: [];
        $data['create_user'] = auth()->id();

        Venue::create($data);

        return redirect()->route('admin.venues.index')
                        ->with('success', 'تم إنشاء المكان بنجاح');
    }

    /**
     * Display the specified venue
     */
    public function show(Venue $venue)
    {
        $venue->load(['events' => function($query) {
            $query->orderBy('created_at', 'desc')->limit(10);
        }]);

        return view('admin.venues.show', compact('venue'));
    }

    /**
     * Show the form for editing the specified venue
     */
    public function edit(Venue $venue)
    {
        return view('admin.venues.edit', compact('venue'));
    }

    /**
     * Update the specified venue
     */
    public function update(Request $request, Venue $venue)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'country' => 'required|string|max:100',
            'capacity' => 'required|integer|min:1',
            'map_lat' => 'nullable|numeric',
            'map_lng' => 'nullable|numeric',
            'facilities' => 'nullable|array',
            'status' => 'required|in:active,inactive'
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                           ->withErrors($validator)
                           ->withInput();
        }

        $data = $request->only([
            'name', 'name_ar', 'address', 'city', 'country', 
            'capacity', 'map_lat', 'map_lng', 'status'
        ]);
        
        $data['facilities'] = $request->facilities ?: [];
        $data['update_user'] = auth()->id();

        $venue->update($data);

        return redirect()->route('admin.venues.index')
                        ->with('success', 'تم تحديث المكان بنجاح');
    }

    /**
     * Remove the specified venue
     */
    public function destroy(Venue $venue)
    {
        // Check if venue has events
        if ($venue->events()->exists()) {
            return redirect()->back()
                           ->with('error', 'لا يمكن حذف المكان لوجود فعاليات مرتبطة به');
        }

        $venue->delete();

        return redirect()->route('admin.venues.index')
                        ->with('success', 'تم حذف المكان بنجاح');
    }
}
