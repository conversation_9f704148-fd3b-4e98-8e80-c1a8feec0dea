<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add new settings for destination selection and website usage control
        $settings = [
            [
                'name' => 'destination_selection_enabled',
                'value' => '1',
                'type' => 'boolean'
            ],
            [
                'name' => 'website_usage_enabled', 
                'value' => '1',
                'type' => 'boolean'
            ]
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['name' => $setting['name']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Setting::whereIn('name', [
            'destination_selection_enabled',
            'website_usage_enabled'
        ])->delete();
    }
};
