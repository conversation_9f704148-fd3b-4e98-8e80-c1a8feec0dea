<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Seller Verification</title>
    <meta name="csrf-token" content="<?php echo csrf_token(); ?>">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .status-box { padding: 15px; margin: 15px 0; border-radius: 8px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-content { background: white; margin: 10% auto; padding: 20px; width: 80%; max-width: 500px; border-radius: 8px; }
        .modal.show { display: block; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار Seller Verification</h1>
        
        <div class="test-section">
            <h3>1️⃣ اختبار JavaScript و AJAX</h3>
            <div id="js-test-result" class="status-box info">جاري الاختبار...</div>
            <button onclick="testJavaScript()" class="btn">اختبار JavaScript</button>
        </div>
        
        <div class="test-section">
            <h3>2️⃣ اختبار Modal</h3>
            <button onclick="showTestModal()" class="btn">عرض Modal</button>
            
            <!-- Test Modal -->
            <div id="testModal" class="modal">
                <div class="modal-content">
                    <h4>اختبار Modal</h4>
                    <form id="testForm" method="POST">
                        <div class="form-group">
                            <label>الحالة:</label>
                            <select name="status" id="test_status">
                                <option value="pending">Pending</option>
                                <option value="approved">Approved</option>
                                <option value="rejected">Rejected</option>
                            </select>
                        </div>
                        <div class="form-group" id="test_rejection_field" style="display: none;">
                            <label>سبب الرفض:</label>
                            <textarea name="rejection_reason" id="test_rejection_reason"></textarea>
                        </div>
                        <button type="button" onclick="testFormSubmit()" class="btn">اختبار الإرسال</button>
                        <button type="button" onclick="closeTestModal()" class="btn" style="background: #6c757d;">إغلاق</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3️⃣ اختبار AJAX Request</h3>
            <div id="ajax-test-result" class="status-box info">لم يتم الاختبار بعد</div>
            <button onclick="testAjaxRequest()" class="btn">اختبار AJAX</button>
        </div>
        
        <div class="test-section">
            <h3>4️⃣ اختبار Routes</h3>
            <div id="routes-test-result" class="status-box info">لم يتم الاختبار بعد</div>
            <button onclick="testRoutes()" class="btn">اختبار Routes</button>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/seller-verification" class="btn">🔙 العودة لـ Seller Verification</a>
        </div>
    </div>

    <!-- Include jQuery -->
    <script src="<?php echo asset('assets/js/jquery.min.js'); ?>"></script>
    
    <script>
        // Set base URL
        window.baseurl = "<?php echo URL::to('/'); ?>/";
        
        // Setup CSRF Token
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        function testJavaScript() {
            const resultDiv = document.getElementById('js-test-result');
            let results = [];
            
            // Test 1: jQuery
            if (typeof jQuery !== 'undefined') {
                results.push('✅ jQuery loaded (version: ' + jQuery.fn.jquery + ')');
            } else {
                results.push('❌ jQuery not loaded');
            }
            
            // Test 2: Base URL
            if (typeof window.baseurl !== 'undefined') {
                results.push('✅ Base URL: ' + window.baseurl);
            } else {
                results.push('❌ Base URL not defined');
            }
            
            // Test 3: CSRF Token
            const csrfToken = $('meta[name="csrf-token"]').attr('content');
            if (csrfToken) {
                results.push('✅ CSRF Token found');
            } else {
                results.push('❌ CSRF Token not found');
            }
            
            resultDiv.innerHTML = results.join('<br>');
            resultDiv.className = 'status-box ' + (results.some(r => r.includes('❌')) ? 'error' : 'success');
        }
        
        function showTestModal() {
            document.getElementById('testModal').classList.add('show');
        }
        
        function closeTestModal() {
            document.getElementById('testModal').classList.remove('show');
        }
        
        // Handle status change
        $('#test_status').on('change', function() {
            const status = $(this).val();
            const rejectionField = $('#test_rejection_field');
            
            if (status === 'rejected') {
                rejectionField.show();
            } else {
                rejectionField.hide();
            }
        });
        
        function testFormSubmit() {
            const form = document.getElementById('testForm');
            const formData = new FormData(form);
            
            console.log('Form data:');
            for (let [key, value] of formData.entries()) {
                console.log(key + ': ' + value);
            }
            
            alert('Form data logged to console. Check browser console for details.');
        }
        
        function testAjaxRequest() {
            const resultDiv = document.getElementById('ajax-test-result');
            resultDiv.innerHTML = '🔄 جاري الاختبار...';
            resultDiv.className = 'status-box info';
            
            // Test AJAX request to a known endpoint
            $.ajax({
                url: window.baseurl + 'seller-verification/verification-field',
                method: 'GET',
                success: function(response) {
                    resultDiv.innerHTML = '✅ AJAX request successful';
                    resultDiv.className = 'status-box success';
                },
                error: function(xhr, status, error) {
                    resultDiv.innerHTML = '❌ AJAX request failed: ' + error + ' (Status: ' + xhr.status + ')';
                    resultDiv.className = 'status-box error';
                    console.error('AJAX Error:', xhr.responseText);
                }
            });
        }
        
        function testRoutes() {
            const resultDiv = document.getElementById('routes-test-result');
            resultDiv.innerHTML = '🔄 جاري اختبار الـ routes...';
            resultDiv.className = 'status-box info';
            
            const routes = [
                'seller-verification',
                'seller-verification/verification-field',
                'seller-verification/verification-requests'
            ];
            
            let results = [];
            let completed = 0;
            
            routes.forEach(route => {
                $.ajax({
                    url: window.baseurl + route,
                    method: 'GET',
                    success: function() {
                        results.push('✅ ' + route);
                        completed++;
                        if (completed === routes.length) {
                            showRouteResults(results, resultDiv);
                        }
                    },
                    error: function(xhr) {
                        results.push('❌ ' + route + ' (Status: ' + xhr.status + ')');
                        completed++;
                        if (completed === routes.length) {
                            showRouteResults(results, resultDiv);
                        }
                    }
                });
            });
        }
        
        function showRouteResults(results, resultDiv) {
            resultDiv.innerHTML = results.join('<br>');
            resultDiv.className = 'status-box ' + (results.some(r => r.includes('❌')) ? 'error' : 'success');
        }
        
        // Auto-run JavaScript test on page load
        $(document).ready(function() {
            testJavaScript();
        });
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('testModal');
            if (event.target === modal) {
                closeTestModal();
            }
        }
    </script>
</body>
</html>
