@extends('layouts.main')

@section('title')
    {{ __('Travel Settings') }}
@endsection

@section('page-title')
    <div class="page-title">
        <div class="row">
            <div class="col-12 col-md-6 order-md-1 order-last">
                <h3>{{ __('Travel Settings') }}</h3>
                <p class="text-subtitle text-muted">{{ __('Manage travel app features and destination settings') }}</p>
            </div>
            <div class="col-12 col-md-6 order-md-2 order-first">
                <nav aria-label="breadcrumb" class="breadcrumb-header float-start float-lg-end">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}">{{ __('Dashboard') }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ __('Travel Settings') }}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <section class="section">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">{{ __('Travel App Settings') }}</h4>
            </div>
            <div class="card-body">
                <form action="{{ route('settings.travel-settings.store') }}" method="POST" class="create-form">
                    @csrf

                    <div class="row">
                        <!-- Destination Selection Setting -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="destination_selection_enabled" class="form-label">
                                    {{ __('Enable Destination Selection') }}
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox"
                                           id="destination_selection_enabled"
                                           name="destination_selection_enabled"
                                           value="1"
                                           {{ (old('destination_selection_enabled', $settings['destination_selection_enabled'] ?? '1') == '1') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="destination_selection_enabled">
                                        {{ __('Allow users to select travel destinations in the app') }}
                                    </label>
                                </div>
                                <small class="text-muted">
                                    {{ __('When enabled, users will see a destination selection interface in the home screen') }}
                                </small>
                            </div>
                        </div>

                        <!-- Website Usage Setting -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="website_usage_enabled" class="form-label">
                                    {{ __('Enable Website Usage') }}
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox"
                                           id="website_usage_enabled"
                                           name="website_usage_enabled"
                                           value="1"
                                           {{ (old('website_usage_enabled', $settings['website_usage_enabled'] ?? '1') == '1') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="website_usage_enabled">
                                        {{ __('Allow users to access the website features') }}
                                    </label>
                                </div>
                                <small class="text-muted">
                                    {{ __('When disabled, website functionality will be restricted in the app') }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Hide Website Header Setting -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="hide_website_header" class="form-label">
                                    {{ __('Hide Website Header') }}
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox"
                                           id="hide_website_header"
                                           name="hide_website_header"
                                           value="1"
                                           {{ (old('hide_website_header', $settings['hide_website_header'] ?? '1') == '1') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="hide_website_header">
                                        {{ __('Hide website navigation header in app') }}
                                    </label>
                                </div>
                                <small class="text-muted">
                                    {{ __('When enabled, the website header/navigation will be hidden in the app') }}
                                </small>
                            </div>
                        </div>

                        <!-- Show Destination by Date Setting -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="show_destination_by_date" class="form-label">
                                    {{ __('Show Destination by Date') }}
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox"
                                           id="show_destination_by_date"
                                           name="show_destination_by_date"
                                           value="1"
                                           {{ (old('show_destination_by_date', $settings['show_destination_by_date'] ?? '1') == '1') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="show_destination_by_date">
                                        {{ __('Filter offers by travel date and destination') }}
                                    </label>
                                </div>
                                <small class="text-muted">
                                    {{ __('When enabled, offers will be filtered based on selected destination and travel dates') }}
                                </small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">{{ __('Important Notes:') }}</h6>
                                <ul class="mb-0">
                                    <li>{{ __('Destination Selection: Controls whether users can choose travel destinations from the app interface') }}</li>
                                    <li>{{ __('Website Usage: Controls access to website features and functionality within the app') }}</li>
                                    <li>{{ __('Changes will take effect immediately for new app sessions') }}</li>
                                    <li>{{ __('Users may need to restart the app to see changes') }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-secondary me-2" onclick="location.reload()">
                                    {{ __('Reset') }}
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Save Settings') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Current Settings Display -->
        <div class="card mt-4">
            <div class="card-header">
                <h4 class="card-title">{{ __('Current Settings Status') }}</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                @if(($settings['destination_selection_enabled'] ?? '1') == '1')
                                    <span class="badge bg-success">{{ __('Enabled') }}</span>
                                @else
                                    <span class="badge bg-danger">{{ __('Disabled') }}</span>
                                @endif
                            </div>
                            <div>
                                <h6 class="mb-0">{{ __('Destination Selection') }}</h6>
                                <small class="text-muted">{{ __('User destination selection interface') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                @if(($settings['website_usage_enabled'] ?? '1') == '1')
                                    <span class="badge bg-success">{{ __('Enabled') }}</span>
                                @else
                                    <span class="badge bg-danger">{{ __('Disabled') }}</span>
                                @endif
                            </div>
                            <div>
                                <h6 class="mb-0">{{ __('Website Usage') }}</h6>
                                <small class="text-muted">{{ __('Website functionality access') }}</small>
                            </div>
                        </div>
                    </div>

                    <!-- New Settings Row -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    @if(($settings['hide_website_header'] ?? '1') == '1')
                                        <span class="badge bg-success">{{ __('Enabled') }}</span>
                                    @else
                                        <span class="badge bg-danger">{{ __('Disabled') }}</span>
                                    @endif
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ __('Hide Website Header') }}</h6>
                                    <small class="text-muted">{{ __('Website header hidden in app') }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-3">
                                <div class="me-3">
                                    @if(($settings['show_destination_by_date'] ?? '1') == '1')
                                        <span class="badge bg-success">{{ __('Enabled') }}</span>
                                    @else
                                        <span class="badge bg-danger">{{ __('Disabled') }}</span>
                                    @endif
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ __('Show Destination by Date') }}</h6>
                                    <small class="text-muted">{{ __('Date-based destination filtering') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            $('.create-form').on('submit', function(e) {
                e.preventDefault();

                let formData = new FormData(this);

                // Handle unchecked checkboxes
                if (!$('#destination_selection_enabled').is(':checked')) {
                    formData.append('destination_selection_enabled', '0');
                }
                if (!$('#website_usage_enabled').is(':checked')) {
                    formData.append('website_usage_enabled', '0');
                }
                if (!$('#hide_website_header').is(':checked')) {
                    formData.append('hide_website_header', '0');
                }
                if (!$('#show_destination_by_date').is(':checked')) {
                    formData.append('show_destination_by_date', '0');
                }

                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.error == false) {
                            showSuccessToast(response.message);
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            showErrorToast(response.message);
                        }
                    },
                    error: function(xhr) {
                        let errors = xhr.responseJSON?.errors;
                        if (errors) {
                            let errorMessage = Object.values(errors).flat().join('\n');
                            showErrorToast(errorMessage);
                        } else {
                            showErrorToast('{{ __("An error occurred while saving settings") }}');
                        }
                    }
                });
            });
        });
    </script>
@endsection
