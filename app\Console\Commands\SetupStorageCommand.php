<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\StorageHelper;

class SetupStorageCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:setup {--force : Force recreation of storage link}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup storage link with fallback for shared hosting';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Setting up storage link...');

        // Check if symbolic link already exists and works
        if (!$this->option('force') && StorageHelper::hasWorkingSymlink()) {
            $this->info('✅ Storage link already exists and is working.');
            return 0;
        }

        // Try to create storage link
        if (StorageHelper::createStorageLink()) {
            if (StorageHelper::hasWorkingSymlink()) {
                $this->info('✅ Storage link created successfully!');
                
                // Check if it's a symbolic link or directory copy
                $linkPath = public_path('storage');
                if (is_link($linkPath)) {
                    $this->info('📁 Using symbolic link method.');
                } else {
                    $this->warn('📁 Using directory copy method (symbolic links not supported).');
                    $this->info('💡 Files will be automatically synced when uploaded.');
                }
            } else {
                $this->error('❌ Storage link created but not working properly.');
                return 1;
            }
        } else {
            $this->error('❌ Failed to create storage link.');
            $this->info('💡 You may need to manually copy files from storage/app/public to public/storage');
            return 1;
        }

        return 0;
    }
}
