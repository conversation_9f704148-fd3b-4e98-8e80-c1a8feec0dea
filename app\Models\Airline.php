<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Airline extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'code',
        'image_id',
        'country',
        'description',
        'status',
        'create_user',
        'update_user'
    ];

    protected $appends = ['image_url'];

    /**
     * Get the image URL
     */
    public function getImageUrlAttribute()
    {
        if ($this->image_id) {
            // Assuming you have a media/file system
            return url(Storage::url("airlines/{$this->image_id}"));
        }
        return null;
    }

    /**
     * Get flights for this airline
     */
    public function flights()
    {
        return $this->hasMany(Flight::class);
    }

    /**
     * Get active flights
     */
    public function activeFlights()
    {
        return $this->hasMany(Flight::class)->where('status', 'scheduled');
    }

    /**
     * Scope for active airlines
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'publish');
    }

    /**
     * Get creator user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    /**
     * Get updater user
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
