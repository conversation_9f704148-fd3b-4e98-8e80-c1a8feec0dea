<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح Modal Seller Verification</title>
    <meta name="csrf-token" content="test-token">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .test-btn { margin: 10px 5px; }
        #console-log { background: #000; color: #0f0; padding: 15px; border-radius: 5px; font-family: monospace; height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح Modal Seller Verification</h1>
        
        <div class="debug-info">
            <h3>المشكلة:</h3>
            <p>عند الضغط على "Save" في modal تغيير حالة seller verification، لا يحدث شيء</p>
        </div>

        <!-- Test Buttons -->
        <div class="mb-3">
            <button class="btn btn-primary test-btn" onclick="testModal()">اختبار فتح Modal</button>
            <button class="btn btn-success test-btn" onclick="testFormSubmit()">اختبار إرسال Form</button>
            <button class="btn btn-warning test-btn" onclick="testJavaScript()">اختبار JavaScript</button>
            <button class="btn btn-info test-btn" onclick="clearConsole()">مسح Console</button>
        </div>

        <!-- Console Log -->
        <div class="debug-info">
            <h4>Console Log:</h4>
            <div id="console-log"></div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="debug-info">
            <h4>نتائج الاختبار:</h4>
            <div id="results-content">لم يتم تشغيل أي اختبار بعد</div>
        </div>

        <!-- Simulated Table Row -->
        <div class="debug-info">
            <h4>محاكاة جدول Seller Verification:</h4>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>User</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>1</td>
                        <td>Test User</td>
                        <td><span class="badge bg-warning">Pending</span></td>
                        <td>
                            <button class="btn btn-sm btn-primary edit_btn" 
                                    data-id="1" 
                                    data-status="pending" 
                                    data-rejection-reason="">
                                Edit
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- The Modal (copied from actual file) -->
        <div id="editStatusModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="myModalLabel1">Status</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form class="edit-form" action="" method="POST" id="statusUpdateForm">
                            <input type="hidden" name="_token" value="test-token">
                            <input type="hidden" name="_method" value="PUT">
                            <div class="row">
                                <div class="col-md-12">
                                    <select name="status" class="form-select" id="verification_status" aria-label="status">
                                        <option value="pending">Pending</option>
                                        <option value="approved">Approved</option>
                                        <option value="rejected">Rejected</option>
                                    </select>
                                </div>
                                <div class="form-group" id="rejectionReasonField" style="display: none;">
                                    <label for="rejection_reason">Rejection Reason <span class="text-danger">*</span></label>
                                    <textarea id="rejection_reason" name="rejection_reason" class="form-control" required></textarea>
                                </div>
                            </div>
                            <input type="submit" value="Save" class="btn btn-primary mt-3">
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Setup
        window.baseurl = "https://admin.demo5150.com/";
        
        // Console logging function
        function logToConsole(message, type = 'info') {
            const console_div = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : type === 'success' ? '#0f0' : type === 'warning' ? '#ff0' : '#0f0';
            console_div.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            console_div.scrollTop = console_div.scrollHeight;
            
            // Also log to browser console
            console.log(message);
        }

        function clearConsole() {
            document.getElementById('console-log').innerHTML = '';
        }

        // Test Functions
        function testJavaScript() {
            logToConsole('🧪 بدء اختبار JavaScript...', 'info');
            
            // Test jQuery
            if (typeof jQuery !== 'undefined') {
                logToConsole('✅ jQuery محمّل (الإصدار: ' + jQuery.fn.jquery + ')', 'success');
            } else {
                logToConsole('❌ jQuery غير محمّل', 'error');
            }
            
            // Test baseurl
            if (typeof window.baseurl !== 'undefined') {
                logToConsole('✅ Base URL: ' + window.baseurl, 'success');
            } else {
                logToConsole('❌ Base URL غير محدد', 'error');
            }
            
            // Test modal elements
            const modal = document.getElementById('editStatusModal');
            const form = document.getElementById('statusUpdateForm');
            const select = document.getElementById('verification_status');
            
            if (modal) logToConsole('✅ Modal موجود', 'success');
            else logToConsole('❌ Modal غير موجود', 'error');
            
            if (form) logToConsole('✅ Form موجود', 'success');
            else logToConsole('❌ Form غير موجود', 'error');
            
            if (select) logToConsole('✅ Select موجود', 'success');
            else logToConsole('❌ Select غير موجود', 'error');
        }

        function testModal() {
            logToConsole('🧪 اختبار فتح Modal...', 'info');
            
            // Simulate clicking edit button
            const row = {
                id: 1,
                status: 'pending',
                rejection_reason: ''
            };
            
            // Set form values
            $('#verification_status').val(row.status).trigger('change');
            $('#rejection_reason').val(row.rejection_reason);
            
            // Set form action
            const actionUrl = window.baseurl + 'seller-verification/' + row.id + '/approval';
            $('#statusUpdateForm').attr('action', actionUrl);
            
            logToConsole('📝 تم تعيين Action URL: ' + actionUrl, 'info');
            logToConsole('📝 تم تعيين Status: ' + row.status, 'info');
            
            // Show modal
            $('#editStatusModal').modal('show');
            logToConsole('✅ تم فتح Modal', 'success');
        }

        function testFormSubmit() {
            logToConsole('🧪 اختبار إرسال Form...', 'info');
            
            const form = document.getElementById('statusUpdateForm');
            const formData = new FormData(form);
            const action = form.getAttribute('action');
            
            logToConsole('📤 Action URL: ' + action, 'info');
            logToConsole('📤 Method: PUT', 'info');
            
            // Log form data
            for (let [key, value] of formData.entries()) {
                logToConsole('📤 ' + key + ': ' + value, 'info');
            }
            
            // Simulate AJAX request
            logToConsole('🔄 محاكاة AJAX request...', 'warning');
            
            $.ajax({
                url: action,
                method: 'PUT',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRF-TOKEN': 'test-token'
                },
                success: function(response) {
                    logToConsole('✅ AJAX نجح: ' + JSON.stringify(response), 'success');
                },
                error: function(xhr, status, error) {
                    logToConsole('❌ AJAX فشل: ' + error + ' (Status: ' + xhr.status + ')', 'error');
                    logToConsole('📄 Response: ' + xhr.responseText, 'error');
                }
            });
        }

        // Event Handlers
        $(document).ready(function() {
            logToConsole('🚀 الصفحة جاهزة', 'success');
            
            // Setup CSRF token
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': 'test-token'
                }
            });
            
            // Handle edit button click
            $('.edit_btn').on('click', function() {
                const id = $(this).data('id');
                const status = $(this).data('status');
                const rejectionReason = $(this).data('rejection-reason');
                
                logToConsole('🖱️ تم الضغط على Edit Button', 'info');
                logToConsole('📝 ID: ' + id + ', Status: ' + status, 'info');
                
                // Set form values
                $('#verification_status').val(status).trigger('change');
                $('#rejection_reason').val(rejectionReason);
                
                // Set form action
                const actionUrl = window.baseurl + 'seller-verification/' + id + '/approval';
                $('#statusUpdateForm').attr('action', actionUrl);
                
                logToConsole('📝 تم تعيين Action: ' + actionUrl, 'info');
                
                // Show modal
                $('#editStatusModal').modal('show');
            });
            
            // Handle status change
            $('#verification_status').on('change', function() {
                const status = $(this).val();
                const rejectionField = $('#rejectionReasonField');
                
                logToConsole('🔄 تم تغيير Status إلى: ' + status, 'info');
                
                if (status === 'rejected') {
                    rejectionField.show();
                    logToConsole('👁️ تم إظهار حقل سبب الرفض', 'info');
                } else {
                    rejectionField.hide();
                    logToConsole('🙈 تم إخفاء حقل سبب الرفض', 'info');
                }
            });
            
            // Handle form submit
            $('.edit-form').on('submit', function(e) {
                e.preventDefault();
                logToConsole('📤 تم إرسال Form', 'warning');
                
                const formElement = $(this);
                const data = new FormData(this);
                const url = $(this).attr('action');
                
                logToConsole('📤 URL: ' + url, 'info');
                
                // Log form data
                for (let [key, value] of data.entries()) {
                    logToConsole('📤 ' + key + ': ' + value, 'info');
                }
                
                // Simulate AJAX
                logToConsole('🔄 إرسال AJAX request...', 'warning');
                
                $.ajax({
                    url: url,
                    method: 'POST', // Laravel will handle _method=PUT
                    data: data,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        logToConsole('✅ تم الحفظ بنجاح!', 'success');
                        $('#editStatusModal').modal('hide');
                    },
                    error: function(xhr, status, error) {
                        logToConsole('❌ خطأ في الحفظ: ' + error, 'error');
                        logToConsole('📄 Response: ' + xhr.responseText, 'error');
                    }
                });
            });
            
            // Auto-run JavaScript test
            setTimeout(testJavaScript, 1000);
        });
    </script>
</body>
</html>
