<?php
// This file was auto-generated from sdk-root/src/data/bedrock-agent-runtime/2023-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-07-26', 'endpointPrefix' => 'bedrock-agent-runtime', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Agents for Amazon Bedrock Runtime', 'serviceId' => 'Bedrock Agent Runtime', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-agent-runtime-2023-07-26', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'DeleteAgentMemory' => [ 'name' => 'DeleteAgentMemory', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/agents/{agentId}/agentAliases/{agentAliasId}/memories', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteAgentMemoryRequest', ], 'output' => [ 'shape' => 'DeleteAgentMemoryResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'GetAgentMemory' => [ 'name' => 'GetAgentMemory', 'http' => [ 'method' => 'GET', 'requestUri' => '/agents/{agentId}/agentAliases/{agentAliasId}/memories', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAgentMemoryRequest', ], 'output' => [ 'shape' => 'GetAgentMemoryResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'InvokeAgent' => [ 'name' => 'InvokeAgent', 'http' => [ 'method' => 'POST', 'requestUri' => '/agents/{agentId}/agentAliases/{agentAliasId}/sessions/{sessionId}/text', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeAgentRequest', ], 'output' => [ 'shape' => 'InvokeAgentResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'InvokeFlow' => [ 'name' => 'InvokeFlow', 'http' => [ 'method' => 'POST', 'requestUri' => '/flows/{flowIdentifier}/aliases/{flowAliasIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'InvokeFlowRequest', ], 'output' => [ 'shape' => 'InvokeFlowResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'Retrieve' => [ 'name' => 'Retrieve', 'http' => [ 'method' => 'POST', 'requestUri' => '/knowledgebases/{knowledgeBaseId}/retrieve', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetrieveRequest', ], 'output' => [ 'shape' => 'RetrieveResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], 'RetrieveAndGenerate' => [ 'name' => 'RetrieveAndGenerate', 'http' => [ 'method' => 'POST', 'requestUri' => '/retrieveAndGenerate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetrieveAndGenerateRequest', ], 'output' => [ 'shape' => 'RetrieveAndGenerateResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'DependencyFailedException', ], [ 'shape' => 'BadGatewayException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActionGroupInvocationInput' => [ 'type' => 'structure', 'members' => [ 'actionGroupName' => [ 'shape' => 'ActionGroupName', ], 'apiPath' => [ 'shape' => 'ApiPath', ], 'executionType' => [ 'shape' => 'ExecutionType', ], 'function' => [ 'shape' => 'Function', ], 'invocationId' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'Parameters', ], 'requestBody' => [ 'shape' => 'RequestBody', ], 'verb' => [ 'shape' => 'Verb', ], ], ], 'ActionGroupInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'ActionGroupOutputString', ], ], ], 'ActionGroupName' => [ 'type' => 'string', 'sensitive' => true, ], 'ActionGroupOutputString' => [ 'type' => 'string', 'sensitive' => true, ], 'ActionInvocationType' => [ 'type' => 'string', 'enum' => [ 'RESULT', 'USER_CONFIRMATION', 'USER_CONFIRMATION_AND_RESULT', ], ], 'AdditionalModelRequestFields' => [ 'type' => 'map', 'key' => [ 'shape' => 'AdditionalModelRequestFieldsKey', ], 'value' => [ 'shape' => 'AdditionalModelRequestFieldsValue', ], ], 'AdditionalModelRequestFieldsKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'AdditionalModelRequestFieldsValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'AgentAliasId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'AgentId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'AgentVersion' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(DRAFT|[0-9]{0,4}[1-9][0-9]{0,4})$', ], 'ApiContentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'PropertyParameters', ], ], 'ApiInvocationInput' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'actionInvocationType' => [ 'shape' => 'ActionInvocationType', ], 'apiPath' => [ 'shape' => 'ApiPath', ], 'httpMethod' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'ApiParameters', ], 'requestBody' => [ 'shape' => 'ApiRequestBody', ], ], ], 'ApiParameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'ApiParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApiParameter', ], ], 'ApiPath' => [ 'type' => 'string', 'sensitive' => true, ], 'ApiRequestBody' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ApiContentMap', ], ], ], 'ApiResult' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'apiPath' => [ 'shape' => 'ApiPath', ], 'confirmationState' => [ 'shape' => 'ConfirmationState', ], 'httpMethod' => [ 'shape' => 'String', ], 'httpStatusCode' => [ 'shape' => 'Integer', ], 'responseBody' => [ 'shape' => 'ResponseBody', ], 'responseState' => [ 'shape' => 'ResponseState', ], ], ], 'Attribution' => [ 'type' => 'structure', 'members' => [ 'citations' => [ 'shape' => 'Citations', ], ], ], 'BadGatewayException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 502, ], 'exception' => true, 'fault' => true, ], 'BedrockModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws(-[^:]+)?:bedrock:[a-z0-9-]{1,20}:(([0-9]{12}:custom-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}/[a-z0-9]{12})|(:foundation-model/[a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))))|(arn:aws(|-us-gov|-cn|-iso|-iso-b):bedrock:(|[0-9a-z-]{1,20}):(|[0-9]{12}):inference-profile/[a-zA-Z0-9-:.]+)|([a-z0-9-]{1,63}[.]{1}[a-z0-9-]{1,63}([.:]?[a-z0-9-]{1,63}))|(([0-9a-zA-Z][_-]?)+)$', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ByteContentBlob' => [ 'type' => 'blob', 'max' => 10485760, 'min' => 1, 'sensitive' => true, ], 'ByteContentDoc' => [ 'type' => 'structure', 'required' => [ 'contentType', 'data', 'identifier', ], 'members' => [ 'contentType' => [ 'shape' => 'ContentType', ], 'data' => [ 'shape' => 'ByteContentBlob', ], 'identifier' => [ 'shape' => 'Identifier', ], ], ], 'ByteContentFile' => [ 'type' => 'structure', 'required' => [ 'data', 'mediaType', ], 'members' => [ 'data' => [ 'shape' => 'ByteContentBlob', ], 'mediaType' => [ 'shape' => 'MimeType', ], ], ], 'Citation' => [ 'type' => 'structure', 'members' => [ 'generatedResponsePart' => [ 'shape' => 'GeneratedResponsePart', ], 'retrievedReferences' => [ 'shape' => 'RetrievedReferences', ], ], ], 'Citations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Citation', ], ], 'CodeInterpreterInvocationInput' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'String', ], 'files' => [ 'shape' => 'Files', ], ], ], 'CodeInterpreterInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'executionError' => [ 'shape' => 'String', ], 'executionOutput' => [ 'shape' => 'String', ], 'executionTimeout' => [ 'shape' => 'Boolean', ], 'files' => [ 'shape' => 'Files', ], ], ], 'ConfirmationState' => [ 'type' => 'string', 'enum' => [ 'CONFIRM', 'DENY', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContentBody' => [ 'type' => 'structure', 'members' => [ 'body' => [ 'shape' => 'String', ], ], ], 'ContentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'Parameters', ], ], 'ContentType' => [ 'type' => 'string', 'pattern' => '[a-z]{1,20}/.{1,20}', ], 'CreationMode' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'OVERRIDDEN', ], ], 'DateTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteAgentMemoryRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', 'location' => 'uri', 'locationName' => 'agentId', ], 'memoryId' => [ 'shape' => 'MemoryId', 'location' => 'querystring', 'locationName' => 'memoryId', ], ], ], 'DeleteAgentMemoryResponse' => [ 'type' => 'structure', 'members' => [], ], 'DependencyFailedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'resourceName' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 424, 'senderFault' => true, ], 'exception' => true, ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'Double' => [ 'type' => 'double', 'box' => true, ], 'ExecutionType' => [ 'type' => 'string', 'enum' => [ 'LAMBDA', 'RETURN_CONTROL', ], ], 'ExternalSource' => [ 'type' => 'structure', 'required' => [ 'sourceType', ], 'members' => [ 'byteContent' => [ 'shape' => 'ByteContentDoc', ], 's3Location' => [ 'shape' => 'S3ObjectDoc', ], 'sourceType' => [ 'shape' => 'ExternalSourceType', ], ], ], 'ExternalSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'BYTE_CONTENT', ], ], 'ExternalSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExternalSource', ], 'max' => 1, 'min' => 1, ], 'ExternalSourcesGenerationConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'inferenceConfig' => [ 'shape' => 'InferenceConfig', ], 'promptTemplate' => [ 'shape' => 'PromptTemplate', ], ], ], 'ExternalSourcesRetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'modelArn', 'sources', ], 'members' => [ 'generationConfiguration' => [ 'shape' => 'ExternalSourcesGenerationConfiguration', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'sources' => [ 'shape' => 'ExternalSources', ], ], ], 'FailureReasonString' => [ 'type' => 'string', 'sensitive' => true, ], 'FailureTrace' => [ 'type' => 'structure', 'members' => [ 'failureReason' => [ 'shape' => 'FailureReasonString', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'FileBody' => [ 'type' => 'blob', 'max' => 1000000, 'min' => 0, 'sensitive' => true, ], 'FilePart' => [ 'type' => 'structure', 'members' => [ 'files' => [ 'shape' => 'OutputFiles', ], ], 'event' => true, ], 'FileSource' => [ 'type' => 'structure', 'required' => [ 'sourceType', ], 'members' => [ 'byteContent' => [ 'shape' => 'ByteContentFile', ], 's3Location' => [ 'shape' => 'S3ObjectFile', ], 'sourceType' => [ 'shape' => 'FileSourceType', ], ], ], 'FileSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', 'BYTE_CONTENT', ], ], 'FileUseCase' => [ 'type' => 'string', 'enum' => [ 'CODE_INTERPRETER', 'CHAT', ], ], 'Files' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'FilterAttribute' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'FilterKey', ], 'value' => [ 'shape' => 'FilterValue', ], ], ], 'FilterKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'FilterValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'FinalResponse' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'FinalResponseString', ], ], ], 'FinalResponseString' => [ 'type' => 'string', 'sensitive' => true, ], 'FlowAliasIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10}/alias/[0-9a-zA-Z]{10})|(\\bTSTALIASID\\b|[0-9a-zA-Z]+)$', ], 'FlowCompletionEvent' => [ 'type' => 'structure', 'required' => [ 'completionReason', ], 'members' => [ 'completionReason' => [ 'shape' => 'FlowCompletionReason', ], ], 'event' => true, 'sensitive' => true, ], 'FlowCompletionReason' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', ], ], 'FlowIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(arn:aws:bedrock:[a-z0-9-]{1,20}:[0-9]{12}:flow/[0-9a-zA-Z]{10})|([0-9a-zA-Z]{10})$', ], 'FlowInput' => [ 'type' => 'structure', 'required' => [ 'content', 'nodeName', 'nodeOutputName', ], 'members' => [ 'content' => [ 'shape' => 'FlowInputContent', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'nodeOutputName' => [ 'shape' => 'NodeOutputName', ], ], ], 'FlowInputContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'sensitive' => true, 'union' => true, ], 'FlowInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'FlowInput', ], 'max' => 1, 'min' => 1, ], 'FlowOutputContent' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'Document', ], ], 'union' => true, ], 'FlowOutputEvent' => [ 'type' => 'structure', 'required' => [ 'content', 'nodeName', 'nodeType', ], 'members' => [ 'content' => [ 'shape' => 'FlowOutputContent', ], 'nodeName' => [ 'shape' => 'NodeName', ], 'nodeType' => [ 'shape' => 'NodeType', ], ], 'event' => true, 'sensitive' => true, ], 'FlowResponseStream' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'conflictException' => [ 'shape' => 'ConflictException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'flowCompletionEvent' => [ 'shape' => 'FlowCompletionEvent', ], 'flowOutputEvent' => [ 'shape' => 'FlowOutputEvent', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'serviceQuotaExceededException' => [ 'shape' => 'ServiceQuotaExceededException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'Function' => [ 'type' => 'string', 'sensitive' => true, ], 'FunctionInvocationInput' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'actionInvocationType' => [ 'shape' => 'ActionInvocationType', ], 'function' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'FunctionParameters', ], ], ], 'FunctionParameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'FunctionParameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionParameter', ], ], 'FunctionResult' => [ 'type' => 'structure', 'required' => [ 'actionGroup', ], 'members' => [ 'actionGroup' => [ 'shape' => 'String', ], 'confirmationState' => [ 'shape' => 'ConfirmationState', ], 'function' => [ 'shape' => 'String', ], 'responseBody' => [ 'shape' => 'ResponseBody', ], 'responseState' => [ 'shape' => 'ResponseState', ], ], ], 'GeneratedResponsePart' => [ 'type' => 'structure', 'members' => [ 'textResponsePart' => [ 'shape' => 'TextResponsePart', ], ], ], 'GenerationConfiguration' => [ 'type' => 'structure', 'members' => [ 'additionalModelRequestFields' => [ 'shape' => 'AdditionalModelRequestFields', ], 'guardrailConfiguration' => [ 'shape' => 'GuardrailConfiguration', ], 'inferenceConfig' => [ 'shape' => 'InferenceConfig', ], 'promptTemplate' => [ 'shape' => 'PromptTemplate', ], ], ], 'GetAgentMemoryRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', 'memoryId', 'memoryType', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', 'location' => 'uri', 'locationName' => 'agentId', ], 'maxItems' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxItems', ], 'memoryId' => [ 'shape' => 'MemoryId', 'location' => 'querystring', 'locationName' => 'memoryId', ], 'memoryType' => [ 'shape' => 'MemoryType', 'location' => 'querystring', 'locationName' => 'memoryType', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'GetAgentMemoryResponse' => [ 'type' => 'structure', 'members' => [ 'memoryContents' => [ 'shape' => 'Memories', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GuadrailAction' => [ 'type' => 'string', 'enum' => [ 'INTERVENED', 'NONE', ], ], 'GuardrailAction' => [ 'type' => 'string', 'enum' => [ 'INTERVENED', 'NONE', ], ], 'GuardrailAssessment' => [ 'type' => 'structure', 'members' => [ 'contentPolicy' => [ 'shape' => 'GuardrailContentPolicyAssessment', ], 'sensitiveInformationPolicy' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAssessment', ], 'topicPolicy' => [ 'shape' => 'GuardrailTopicPolicyAssessment', ], 'wordPolicy' => [ 'shape' => 'GuardrailWordPolicyAssessment', ], ], 'sensitive' => true, ], 'GuardrailAssessmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailAssessment', ], ], 'GuardrailConfiguration' => [ 'type' => 'structure', 'required' => [ 'guardrailId', 'guardrailVersion', ], 'members' => [ 'guardrailId' => [ 'shape' => 'GuardrailConfigurationGuardrailIdString', ], 'guardrailVersion' => [ 'shape' => 'GuardrailConfigurationGuardrailVersionString', ], ], ], 'GuardrailConfigurationGuardrailIdString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '^[a-z0-9]+$', ], 'GuardrailConfigurationGuardrailVersionString' => [ 'type' => 'string', 'max' => 5, 'min' => 1, 'pattern' => '^(([1-9][0-9]{0,7})|(DRAFT))$', ], 'GuardrailContentFilter' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailContentPolicyAction', ], 'confidence' => [ 'shape' => 'GuardrailContentFilterConfidence', ], 'type' => [ 'shape' => 'GuardrailContentFilterType', ], ], 'sensitive' => true, ], 'GuardrailContentFilterConfidence' => [ 'type' => 'string', 'enum' => [ 'NONE', 'LOW', 'MEDIUM', 'HIGH', ], ], 'GuardrailContentFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailContentFilter', ], 'sensitive' => true, ], 'GuardrailContentFilterType' => [ 'type' => 'string', 'enum' => [ 'INSULTS', 'HATE', 'SEXUAL', 'VIOLENCE', 'MISCONDUCT', 'PROMPT_ATTACK', ], ], 'GuardrailContentPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailContentPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'GuardrailContentFilterList', ], ], 'sensitive' => true, ], 'GuardrailCustomWord' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailWordPolicyAction', ], 'match' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'GuardrailCustomWordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailCustomWord', ], 'sensitive' => true, ], 'GuardrailManagedWord' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailWordPolicyAction', ], 'match' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailManagedWordType', ], ], 'sensitive' => true, ], 'GuardrailManagedWordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailManagedWord', ], 'sensitive' => true, ], 'GuardrailManagedWordType' => [ 'type' => 'string', 'enum' => [ 'PROFANITY', ], ], 'GuardrailPiiEntityFilter' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAction', ], 'match' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailPiiEntityType', ], ], 'sensitive' => true, ], 'GuardrailPiiEntityFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPiiEntityFilter', ], 'sensitive' => true, ], 'GuardrailPiiEntityType' => [ 'type' => 'string', 'enum' => [ 'ADDRESS', 'AGE', 'AWS_ACCESS_KEY', 'AWS_SECRET_KEY', 'CA_HEALTH_NUMBER', 'CA_SOCIAL_INSURANCE_NUMBER', 'CREDIT_DEBIT_CARD_CVV', 'CREDIT_DEBIT_CARD_EXPIRY', 'CREDIT_DEBIT_CARD_NUMBER', 'DRIVER_ID', 'EMAIL', 'INTERNATIONAL_BANK_ACCOUNT_NUMBER', 'IP_ADDRESS', 'LICENSE_PLATE', 'MAC_ADDRESS', 'NAME', 'PASSWORD', 'PHONE', 'PIN', 'SWIFT_CODE', 'UK_NATIONAL_HEALTH_SERVICE_NUMBER', 'UK_NATIONAL_INSURANCE_NUMBER', 'UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER', 'URL', 'USERNAME', 'US_BANK_ACCOUNT_NUMBER', 'US_BANK_ROUTING_NUMBER', 'US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER', 'US_PASSPORT_NUMBER', 'US_SOCIAL_SECURITY_NUMBER', 'VEHICLE_IDENTIFICATION_NUMBER', ], ], 'GuardrailRegexFilter' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailSensitiveInformationPolicyAction', ], 'match' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'regex' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'GuardrailRegexFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailRegexFilter', ], 'sensitive' => true, ], 'GuardrailSensitiveInformationPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', 'ANONYMIZED', ], ], 'GuardrailSensitiveInformationPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'piiEntities' => [ 'shape' => 'GuardrailPiiEntityFilterList', ], 'regexes' => [ 'shape' => 'GuardrailRegexFilterList', ], ], 'sensitive' => true, ], 'GuardrailTopic' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailTopicPolicyAction', ], 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'GuardrailTopicType', ], ], 'sensitive' => true, ], 'GuardrailTopicList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailTopic', ], 'sensitive' => true, ], 'GuardrailTopicPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailTopicPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'topics' => [ 'shape' => 'GuardrailTopicList', ], ], 'sensitive' => true, ], 'GuardrailTopicType' => [ 'type' => 'string', 'enum' => [ 'DENY', ], ], 'GuardrailTrace' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'GuardrailAction', ], 'inputAssessments' => [ 'shape' => 'GuardrailAssessmentList', ], 'outputAssessments' => [ 'shape' => 'GuardrailAssessmentList', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'GuardrailWordPolicyAction' => [ 'type' => 'string', 'enum' => [ 'BLOCKED', ], ], 'GuardrailWordPolicyAssessment' => [ 'type' => 'structure', 'members' => [ 'customWords' => [ 'shape' => 'GuardrailCustomWordList', ], 'managedWordLists' => [ 'shape' => 'GuardrailManagedWordList', ], ], 'sensitive' => true, ], 'Identifier' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'InferenceConfig' => [ 'type' => 'structure', 'members' => [ 'textInferenceConfig' => [ 'shape' => 'TextInferenceConfig', ], ], ], 'InferenceConfiguration' => [ 'type' => 'structure', 'members' => [ 'maximumLength' => [ 'shape' => 'MaximumLength', ], 'stopSequences' => [ 'shape' => 'StopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topK' => [ 'shape' => 'TopK', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'InputFile' => [ 'type' => 'structure', 'required' => [ 'name', 'source', 'useCase', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'source' => [ 'shape' => 'FileSource', ], 'useCase' => [ 'shape' => 'FileUseCase', ], ], ], 'InputFiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputFile', ], ], 'InputText' => [ 'type' => 'string', 'max' => 25000000, 'min' => 0, 'sensitive' => true, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvocationInput' => [ 'type' => 'structure', 'members' => [ 'actionGroupInvocationInput' => [ 'shape' => 'ActionGroupInvocationInput', ], 'codeInterpreterInvocationInput' => [ 'shape' => 'CodeInterpreterInvocationInput', ], 'invocationType' => [ 'shape' => 'InvocationType', ], 'knowledgeBaseLookupInput' => [ 'shape' => 'KnowledgeBaseLookupInput', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'InvocationInputMember' => [ 'type' => 'structure', 'members' => [ 'apiInvocationInput' => [ 'shape' => 'ApiInvocationInput', ], 'functionInvocationInput' => [ 'shape' => 'FunctionInvocationInput', ], ], 'union' => true, ], 'InvocationInputs' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvocationInputMember', ], 'max' => 5, 'min' => 1, ], 'InvocationResultMember' => [ 'type' => 'structure', 'members' => [ 'apiResult' => [ 'shape' => 'ApiResult', ], 'functionResult' => [ 'shape' => 'FunctionResult', ], ], 'union' => true, ], 'InvocationType' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'FINISH', 'ACTION_GROUP_CODE_INTERPRETER', ], ], 'InvokeAgentRequest' => [ 'type' => 'structure', 'required' => [ 'agentAliasId', 'agentId', 'sessionId', ], 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', 'location' => 'uri', 'locationName' => 'agentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', 'location' => 'uri', 'locationName' => 'agentId', ], 'enableTrace' => [ 'shape' => 'Boolean', ], 'endSession' => [ 'shape' => 'Boolean', ], 'inputText' => [ 'shape' => 'InputText', ], 'memoryId' => [ 'shape' => 'MemoryId', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'uri', 'locationName' => 'sessionId', ], 'sessionState' => [ 'shape' => 'SessionState', ], ], ], 'InvokeAgentResponse' => [ 'type' => 'structure', 'required' => [ 'completion', 'contentType', 'sessionId', ], 'members' => [ 'completion' => [ 'shape' => 'ResponseStream', ], 'contentType' => [ 'shape' => 'MimeType', 'location' => 'header', 'locationName' => 'x-amzn-bedrock-agent-content-type', ], 'memoryId' => [ 'shape' => 'MemoryId', 'location' => 'header', 'locationName' => 'x-amz-bedrock-agent-memory-id', ], 'sessionId' => [ 'shape' => 'SessionId', 'location' => 'header', 'locationName' => 'x-amz-bedrock-agent-session-id', ], ], 'payload' => 'completion', ], 'InvokeFlowRequest' => [ 'type' => 'structure', 'required' => [ 'flowAliasIdentifier', 'flowIdentifier', 'inputs', ], 'members' => [ 'flowAliasIdentifier' => [ 'shape' => 'FlowAliasIdentifier', 'location' => 'uri', 'locationName' => 'flowAliasIdentifier', ], 'flowIdentifier' => [ 'shape' => 'FlowIdentifier', 'location' => 'uri', 'locationName' => 'flowIdentifier', ], 'inputs' => [ 'shape' => 'FlowInputs', ], ], ], 'InvokeFlowResponse' => [ 'type' => 'structure', 'required' => [ 'responseStream', ], 'members' => [ 'responseStream' => [ 'shape' => 'FlowResponseStream', ], ], 'payload' => 'responseStream', ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws(|-cn|-us-gov):kms:[a-zA-Z0-9-]*:[0-9]{12}:key/[a-zA-Z0-9-]{36}$', ], 'KnowledgeBaseConfiguration' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'retrievalConfiguration', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], ], ], 'KnowledgeBaseConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseConfiguration', ], 'min' => 1, ], 'KnowledgeBaseId' => [ 'type' => 'string', 'max' => 10, 'min' => 0, 'pattern' => '^[0-9a-zA-Z]+$', ], 'KnowledgeBaseLookupInput' => [ 'type' => 'structure', 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'TraceKnowledgeBaseId', ], 'text' => [ 'shape' => 'KnowledgeBaseLookupInputString', ], ], ], 'KnowledgeBaseLookupInputString' => [ 'type' => 'string', 'sensitive' => true, ], 'KnowledgeBaseLookupOutput' => [ 'type' => 'structure', 'members' => [ 'retrievedReferences' => [ 'shape' => 'RetrievedReferences', ], ], ], 'KnowledgeBaseQuery' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'KnowledgeBaseQueryTextString', ], ], 'sensitive' => true, ], 'KnowledgeBaseQueryTextString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'KnowledgeBaseRetrievalConfiguration' => [ 'type' => 'structure', 'required' => [ 'vectorSearchConfiguration', ], 'members' => [ 'vectorSearchConfiguration' => [ 'shape' => 'KnowledgeBaseVectorSearchConfiguration', ], ], ], 'KnowledgeBaseRetrievalResult' => [ 'type' => 'structure', 'required' => [ 'content', ], 'members' => [ 'content' => [ 'shape' => 'RetrievalResultContent', ], 'location' => [ 'shape' => 'RetrievalResultLocation', ], 'metadata' => [ 'shape' => 'RetrievalResultMetadata', ], 'score' => [ 'shape' => 'Double', ], ], ], 'KnowledgeBaseRetrievalResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'KnowledgeBaseRetrievalResult', ], 'sensitive' => true, ], 'KnowledgeBaseRetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'modelArn', ], 'members' => [ 'generationConfiguration' => [ 'shape' => 'GenerationConfiguration', ], 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', ], 'modelArn' => [ 'shape' => 'BedrockModelArn', ], 'orchestrationConfiguration' => [ 'shape' => 'OrchestrationConfiguration', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], ], ], 'KnowledgeBaseVectorSearchConfiguration' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'RetrievalFilter', ], 'numberOfResults' => [ 'shape' => 'KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger', 'box' => true, ], 'overrideSearchType' => [ 'shape' => 'SearchType', ], ], ], 'KnowledgeBaseVectorSearchConfigurationNumberOfResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'LambdaArn' => [ 'type' => 'string', ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'MaxTokens' => [ 'type' => 'integer', 'box' => true, 'max' => 65536, 'min' => 0, ], 'MaximumLength' => [ 'type' => 'integer', 'box' => true, 'max' => 4096, 'min' => 0, ], 'Memories' => [ 'type' => 'list', 'member' => [ 'shape' => 'Memory', ], ], 'Memory' => [ 'type' => 'structure', 'members' => [ 'sessionSummary' => [ 'shape' => 'MemorySessionSummary', ], ], 'union' => true, ], 'MemoryId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._:-]+$', ], 'MemorySessionSummary' => [ 'type' => 'structure', 'members' => [ 'memoryId' => [ 'shape' => 'MemoryId', ], 'sessionExpiryTime' => [ 'shape' => 'DateTimestamp', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'sessionStartTime' => [ 'shape' => 'DateTimestamp', ], 'summaryText' => [ 'shape' => 'SummaryText', ], ], ], 'MemoryType' => [ 'type' => 'string', 'enum' => [ 'SESSION_SUMMARY', ], ], 'Metadata' => [ 'type' => 'structure', 'members' => [ 'usage' => [ 'shape' => 'Usage', ], ], 'sensitive' => true, ], 'MimeType' => [ 'type' => 'string', ], 'ModelInvocationInput' => [ 'type' => 'structure', 'members' => [ 'inferenceConfiguration' => [ 'shape' => 'InferenceConfiguration', ], 'overrideLambda' => [ 'shape' => 'LambdaArn', ], 'parserMode' => [ 'shape' => 'CreationMode', ], 'promptCreationMode' => [ 'shape' => 'CreationMode', ], 'text' => [ 'shape' => 'PromptText', ], 'traceId' => [ 'shape' => 'TraceId', ], 'type' => [ 'shape' => 'PromptType', ], ], 'sensitive' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^\\S*$', ], 'NodeName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$', ], 'NodeOutputName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]([_]?[0-9a-zA-Z]){0,99}$', ], 'NodeType' => [ 'type' => 'string', 'enum' => [ 'FlowInputNode', 'FlowOutputNode', 'LambdaFunctionNode', 'KnowledgeBaseNode', 'PromptNode', 'ConditionNode', 'LexNode', ], ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '^[\\s\\S]*$', ], 'Observation' => [ 'type' => 'structure', 'members' => [ 'actionGroupInvocationOutput' => [ 'shape' => 'ActionGroupInvocationOutput', ], 'codeInterpreterInvocationOutput' => [ 'shape' => 'CodeInterpreterInvocationOutput', ], 'finalResponse' => [ 'shape' => 'FinalResponse', ], 'knowledgeBaseLookupOutput' => [ 'shape' => 'KnowledgeBaseLookupOutput', ], 'repromptResponse' => [ 'shape' => 'RepromptResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], 'type' => [ 'shape' => 'Type', ], ], 'sensitive' => true, ], 'OrchestrationConfiguration' => [ 'type' => 'structure', 'required' => [ 'queryTransformationConfiguration', ], 'members' => [ 'queryTransformationConfiguration' => [ 'shape' => 'QueryTransformationConfiguration', ], ], ], 'OrchestrationModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'metadata' => [ 'shape' => 'Metadata', ], 'rawResponse' => [ 'shape' => 'RawResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'OrchestrationTrace' => [ 'type' => 'structure', 'members' => [ 'invocationInput' => [ 'shape' => 'InvocationInput', ], 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'OrchestrationModelInvocationOutput', ], 'observation' => [ 'shape' => 'Observation', ], 'rationale' => [ 'shape' => 'Rationale', ], ], 'sensitive' => true, 'union' => true, ], 'OutputFile' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'FileBody', ], 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'MimeType', ], ], 'sensitive' => true, ], 'OutputFiles' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputFile', ], 'max' => 5, 'min' => 0, ], 'OutputString' => [ 'type' => 'string', 'sensitive' => true, ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], ], 'ParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'Parameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'PartBody' => [ 'type' => 'blob', 'max' => 1000000, 'min' => 0, 'sensitive' => true, ], 'PayloadPart' => [ 'type' => 'structure', 'members' => [ 'attribution' => [ 'shape' => 'Attribution', ], 'bytes' => [ 'shape' => 'PartBody', ], ], 'event' => true, 'sensitive' => true, ], 'PostProcessingModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'parsedResponse' => [ 'shape' => 'PostProcessingParsedResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'PostProcessingParsedResponse' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'OutputString', ], ], 'sensitive' => true, ], 'PostProcessingTrace' => [ 'type' => 'structure', 'members' => [ 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'PostProcessingModelInvocationOutput', ], ], 'sensitive' => true, 'union' => true, ], 'PreProcessingModelInvocationOutput' => [ 'type' => 'structure', 'members' => [ 'parsedResponse' => [ 'shape' => 'PreProcessingParsedResponse', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'PreProcessingParsedResponse' => [ 'type' => 'structure', 'members' => [ 'isValid' => [ 'shape' => 'Boolean', ], 'rationale' => [ 'shape' => 'RationaleString', ], ], 'sensitive' => true, ], 'PreProcessingTrace' => [ 'type' => 'structure', 'members' => [ 'modelInvocationInput' => [ 'shape' => 'ModelInvocationInput', ], 'modelInvocationOutput' => [ 'shape' => 'PreProcessingModelInvocationOutput', ], ], 'sensitive' => true, 'union' => true, ], 'PromptSessionAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'PromptTemplate' => [ 'type' => 'structure', 'members' => [ 'textPromptTemplate' => [ 'shape' => 'TextPromptTemplate', ], ], ], 'PromptText' => [ 'type' => 'string', 'sensitive' => true, ], 'PromptType' => [ 'type' => 'string', 'enum' => [ 'PRE_PROCESSING', 'ORCHESTRATION', 'KNOWLEDGE_BASE_RESPONSE_GENERATION', 'POST_PROCESSING', ], ], 'PropertyParameters' => [ 'type' => 'structure', 'members' => [ 'properties' => [ 'shape' => 'ParameterList', ], ], ], 'QueryTransformationConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'QueryTransformationType', ], ], ], 'QueryTransformationType' => [ 'type' => 'string', 'enum' => [ 'QUERY_DECOMPOSITION', ], ], 'RAGStopSequences' => [ 'type' => 'list', 'member' => [ 'shape' => 'RAGStopSequencesMemberString', ], 'max' => 4, 'min' => 0, ], 'RAGStopSequencesMemberString' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, ], 'Rationale' => [ 'type' => 'structure', 'members' => [ 'text' => [ 'shape' => 'RationaleString', ], 'traceId' => [ 'shape' => 'TraceId', ], ], 'sensitive' => true, ], 'RationaleString' => [ 'type' => 'string', 'sensitive' => true, ], 'RawResponse' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RepromptResponse' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'Source', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RequestBody' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'ContentMap', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResponseBody' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ContentBody', ], ], 'ResponseState' => [ 'type' => 'string', 'enum' => [ 'FAILURE', 'REPROMPT', ], ], 'ResponseStream' => [ 'type' => 'structure', 'members' => [ 'accessDeniedException' => [ 'shape' => 'AccessDeniedException', ], 'badGatewayException' => [ 'shape' => 'BadGatewayException', ], 'chunk' => [ 'shape' => 'PayloadPart', ], 'conflictException' => [ 'shape' => 'ConflictException', ], 'dependencyFailedException' => [ 'shape' => 'DependencyFailedException', ], 'files' => [ 'shape' => 'FilePart', ], 'internalServerException' => [ 'shape' => 'InternalServerException', ], 'resourceNotFoundException' => [ 'shape' => 'ResourceNotFoundException', ], 'returnControl' => [ 'shape' => 'ReturnControlPayload', ], 'serviceQuotaExceededException' => [ 'shape' => 'ServiceQuotaExceededException', ], 'throttlingException' => [ 'shape' => 'ThrottlingException', ], 'trace' => [ 'shape' => 'TracePart', ], 'validationException' => [ 'shape' => 'ValidationException', ], ], 'eventstream' => true, ], 'RetrievalFilter' => [ 'type' => 'structure', 'members' => [ 'andAll' => [ 'shape' => 'RetrievalFilterList', ], 'equals' => [ 'shape' => 'FilterAttribute', ], 'greaterThan' => [ 'shape' => 'FilterAttribute', ], 'greaterThanOrEquals' => [ 'shape' => 'FilterAttribute', ], 'in' => [ 'shape' => 'FilterAttribute', ], 'lessThan' => [ 'shape' => 'FilterAttribute', ], 'lessThanOrEquals' => [ 'shape' => 'FilterAttribute', ], 'listContains' => [ 'shape' => 'FilterAttribute', ], 'notEquals' => [ 'shape' => 'FilterAttribute', ], 'notIn' => [ 'shape' => 'FilterAttribute', ], 'orAll' => [ 'shape' => 'RetrievalFilterList', ], 'startsWith' => [ 'shape' => 'FilterAttribute', ], 'stringContains' => [ 'shape' => 'FilterAttribute', ], ], 'sensitive' => true, 'union' => true, ], 'RetrievalFilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievalFilter', ], 'min' => 2, ], 'RetrievalResultConfluenceLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrievalResultContent' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RetrievalResultLocation' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'confluenceLocation' => [ 'shape' => 'RetrievalResultConfluenceLocation', ], 's3Location' => [ 'shape' => 'RetrievalResultS3Location', ], 'salesforceLocation' => [ 'shape' => 'RetrievalResultSalesforceLocation', ], 'sharePointLocation' => [ 'shape' => 'RetrievalResultSharePointLocation', ], 'type' => [ 'shape' => 'RetrievalResultLocationType', ], 'webLocation' => [ 'shape' => 'RetrievalResultWebLocation', ], ], 'sensitive' => true, ], 'RetrievalResultLocationType' => [ 'type' => 'string', 'enum' => [ 'S3', 'WEB', 'CONFLUENCE', 'SALESFORCE', 'SHAREPOINT', ], ], 'RetrievalResultMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'RetrievalResultMetadataKey', ], 'value' => [ 'shape' => 'RetrievalResultMetadataValue', ], 'min' => 1, 'sensitive' => true, ], 'RetrievalResultMetadataKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'RetrievalResultMetadataValue' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'RetrievalResultS3Location' => [ 'type' => 'structure', 'members' => [ 'uri' => [ 'shape' => 'String', ], ], ], 'RetrievalResultSalesforceLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrievalResultSharePointLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrievalResultWebLocation' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'String', ], ], ], 'RetrieveAndGenerateConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'externalSourcesConfiguration' => [ 'shape' => 'ExternalSourcesRetrieveAndGenerateConfiguration', ], 'knowledgeBaseConfiguration' => [ 'shape' => 'KnowledgeBaseRetrieveAndGenerateConfiguration', ], 'type' => [ 'shape' => 'RetrieveAndGenerateType', ], ], ], 'RetrieveAndGenerateInput' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'RetrieveAndGenerateInputTextString', ], ], 'sensitive' => true, ], 'RetrieveAndGenerateInputTextString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'RetrieveAndGenerateOutput' => [ 'type' => 'structure', 'required' => [ 'text', ], 'members' => [ 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'RetrieveAndGenerateRequest' => [ 'type' => 'structure', 'required' => [ 'input', ], 'members' => [ 'input' => [ 'shape' => 'RetrieveAndGenerateInput', ], 'retrieveAndGenerateConfiguration' => [ 'shape' => 'RetrieveAndGenerateConfiguration', ], 'sessionConfiguration' => [ 'shape' => 'RetrieveAndGenerateSessionConfiguration', ], 'sessionId' => [ 'shape' => 'SessionId', ], ], ], 'RetrieveAndGenerateResponse' => [ 'type' => 'structure', 'required' => [ 'output', 'sessionId', ], 'members' => [ 'citations' => [ 'shape' => 'Citations', ], 'guardrailAction' => [ 'shape' => 'GuadrailAction', ], 'output' => [ 'shape' => 'RetrieveAndGenerateOutput', ], 'sessionId' => [ 'shape' => 'SessionId', ], ], ], 'RetrieveAndGenerateSessionConfiguration' => [ 'type' => 'structure', 'required' => [ 'kmsKeyArn', ], 'members' => [ 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'RetrieveAndGenerateType' => [ 'type' => 'string', 'enum' => [ 'KNOWLEDGE_BASE', 'EXTERNAL_SOURCES', ], ], 'RetrieveRequest' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseId', 'retrievalQuery', ], 'members' => [ 'knowledgeBaseId' => [ 'shape' => 'KnowledgeBaseId', 'location' => 'uri', 'locationName' => 'knowledgeBaseId', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'retrievalConfiguration' => [ 'shape' => 'KnowledgeBaseRetrievalConfiguration', ], 'retrievalQuery' => [ 'shape' => 'KnowledgeBaseQuery', ], ], ], 'RetrieveResponse' => [ 'type' => 'structure', 'required' => [ 'retrievalResults', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'retrievalResults' => [ 'shape' => 'KnowledgeBaseRetrievalResults', ], ], ], 'RetrievedReference' => [ 'type' => 'structure', 'members' => [ 'content' => [ 'shape' => 'RetrievalResultContent', ], 'location' => [ 'shape' => 'RetrievalResultLocation', ], 'metadata' => [ 'shape' => 'RetrievalResultMetadata', ], ], ], 'RetrievedReferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'RetrievedReference', ], ], 'ReturnControlInvocationResults' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvocationResultMember', ], 'max' => 5, 'min' => 1, ], 'ReturnControlPayload' => [ 'type' => 'structure', 'members' => [ 'invocationId' => [ 'shape' => 'String', ], 'invocationInputs' => [ 'shape' => 'InvocationInputs', ], ], 'event' => true, 'sensitive' => true, ], 'S3ObjectDoc' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'uri' => [ 'shape' => 'S3Uri', ], ], ], 'S3ObjectFile' => [ 'type' => 'structure', 'required' => [ 'uri', ], 'members' => [ 'uri' => [ 'shape' => 'S3Uri', ], ], ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^s3://[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]/.{1,1024}$', ], 'SearchType' => [ 'type' => 'string', 'enum' => [ 'HYBRID', 'SEMANTIC', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SessionAttributesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'SessionId' => [ 'type' => 'string', 'max' => 100, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._:-]+$', ], 'SessionState' => [ 'type' => 'structure', 'members' => [ 'files' => [ 'shape' => 'InputFiles', ], 'invocationId' => [ 'shape' => 'String', ], 'knowledgeBaseConfigurations' => [ 'shape' => 'KnowledgeBaseConfigurations', ], 'promptSessionAttributes' => [ 'shape' => 'PromptSessionAttributesMap', ], 'returnControlInvocationResults' => [ 'shape' => 'ReturnControlInvocationResults', ], 'sessionAttributes' => [ 'shape' => 'SessionAttributesMap', ], ], ], 'Source' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'PARSER', ], 'sensitive' => true, ], 'Span' => [ 'type' => 'structure', 'members' => [ 'end' => [ 'shape' => 'SpanEndInteger', ], 'start' => [ 'shape' => 'SpanStartInteger', ], ], ], 'SpanEndInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'SpanStartInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'StopSequences' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 4, 'min' => 0, ], 'String' => [ 'type' => 'string', ], 'SummaryText' => [ 'type' => 'string', 'max' => 25000000, 'min' => 0, ], 'Temperature' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'TextInferenceConfig' => [ 'type' => 'structure', 'members' => [ 'maxTokens' => [ 'shape' => 'MaxTokens', ], 'stopSequences' => [ 'shape' => 'RAGStopSequences', ], 'temperature' => [ 'shape' => 'Temperature', ], 'topP' => [ 'shape' => 'TopP', ], ], ], 'TextPromptTemplate' => [ 'type' => 'string', 'max' => 4000, 'min' => 1, 'sensitive' => true, ], 'TextResponsePart' => [ 'type' => 'structure', 'members' => [ 'span' => [ 'shape' => 'Span', ], 'text' => [ 'shape' => 'String', ], ], 'sensitive' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TopK' => [ 'type' => 'integer', 'box' => true, 'max' => 500, 'min' => 0, ], 'TopP' => [ 'type' => 'float', 'box' => true, 'max' => 1, 'min' => 0, ], 'Trace' => [ 'type' => 'structure', 'members' => [ 'failureTrace' => [ 'shape' => 'FailureTrace', ], 'guardrailTrace' => [ 'shape' => 'GuardrailTrace', ], 'orchestrationTrace' => [ 'shape' => 'OrchestrationTrace', ], 'postProcessingTrace' => [ 'shape' => 'PostProcessingTrace', ], 'preProcessingTrace' => [ 'shape' => 'PreProcessingTrace', ], ], 'sensitive' => true, 'union' => true, ], 'TraceId' => [ 'type' => 'string', 'max' => 16, 'min' => 2, ], 'TraceKnowledgeBaseId' => [ 'type' => 'string', 'sensitive' => true, ], 'TracePart' => [ 'type' => 'structure', 'members' => [ 'agentAliasId' => [ 'shape' => 'AgentAliasId', ], 'agentId' => [ 'shape' => 'AgentId', ], 'agentVersion' => [ 'shape' => 'AgentVersion', ], 'sessionId' => [ 'shape' => 'SessionId', ], 'trace' => [ 'shape' => 'Trace', ], ], 'event' => true, 'sensitive' => true, ], 'Type' => [ 'type' => 'string', 'enum' => [ 'ACTION_GROUP', 'KNOWLEDGE_BASE', 'FINISH', 'ASK_USER', 'REPROMPT', ], ], 'Usage' => [ 'type' => 'structure', 'members' => [ 'inputTokens' => [ 'shape' => 'Integer', ], 'outputTokens' => [ 'shape' => 'Integer', ], ], 'sensitive' => true, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Verb' => [ 'type' => 'string', 'sensitive' => true, ], ],];
