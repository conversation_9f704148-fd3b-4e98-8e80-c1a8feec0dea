<?php
/**
 * Travel Offers - Installation Status Check
 * Quick status check for installation progress
 *
 * <AUTHOR>
 * @project Travel Offers
 */

echo "<h1>🎯 Travel Offers - Installation Status</h1>";

echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📊 Installation Progress</h2>";

// Check installation steps
$steps = [
    'Server Requirements' => 'install/server',
    'Folder Permissions' => 'install/folders',
    'PHP Functions' => 'install/php-function',
    'Database Setup' => 'install/database',
    'Migrations' => 'install/migrations',
    'Keys Setup' => 'install/keys',
    'Finish' => 'install/finish'
];

echo "<ol>";
foreach ($steps as $step => $url) {
    echo "<li><a href='$url' style='color: #059669; text-decoration: none;'>$step</a></li>";
}
echo "</ol>";
echo "</div>";

echo "<div style='background: #dcfce7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>✅ Current Status</h2>";

// Check PHP functions
echo "<h3>PHP Functions Check:</h3>";
echo "<p><strong>Symlink:</strong> " . (function_exists('symlink') ? '✅ Enabled' : '❌ Disabled') . "</p>";
echo "<p><strong>Open BaseDir:</strong> " . (empty(ini_get('open_basedir')) ? '✅ Disabled (Good)' : '❌ Enabled (Bad)') . "</p>";

// Check required extensions
echo "<h3>Required Extensions:</h3>";
$required_extensions = ['mysqli', 'pdo_mysql', 'gd', 'curl', 'mbstring', 'openssl', 'fileinfo', 'zip'];
foreach ($required_extensions as $ext) {
    echo "<p><strong>$ext:</strong> " . (extension_loaded($ext) ? '✅ Loaded' : '❌ Missing') . "</p>";
}

// Check folder permissions
echo "<h3>Folder Permissions:</h3>";
$folders = [
    '../storage/framework' => 'Storage Framework',
    '../storage/logs' => 'Storage Logs',
    '../bootstrap/cache' => 'Bootstrap Cache'
];

foreach ($folders as $path => $name) {
    $writable = is_writable($path);
    echo "<p><strong>$name:</strong> " . ($writable ? '✅ Writable' : '❌ Not Writable') . "</p>";
}

echo "</div>";

echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔗 Quick Actions</h2>";
echo "<p><a href='install/folders' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Continue Installation</a></p>";
echo "<p><a href='install/database' style='background: #0369a1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Setup Database</a></p>";
echo "<p><a href='/' style='background: #7c3aed; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Go to Admin Panel</a></p>";
echo "</div>";

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<p><strong>eClassify Travel Offers</strong> - Installation Helper</p>";
echo "<p>Developed by AmrDev | License Verification Disabled</p>";
echo "</div>";
?>
