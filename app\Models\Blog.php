<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class Blog extends Model {
    use HasFactory;

    protected $dates = ['created_at', 'updated_at'];

    protected $fillable = [
        'title',
        'slug',
        'description',
        'image',
        'tags'
    ];

    public function category() {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function getImageAttribute($image) {
        if (empty($image)) {
            return $image;
        }

        // Use StorageHelper for better compatibility
        if (class_exists('\App\Helpers\StorageHelper')) {
            return \App\Helpers\StorageHelper::url($image);
        }

        // Fallback to original method
        return url(Storage::url($image));
    }

    public function getTagsAttribute($value) {
        if (!empty($value)) {
            return explode(',', $value);
        }
        return $value;
    }

    public function setTagsAttribute($value) {
        return $this->attributes['tags'] = implode(',', $value);
    }

    public function scopeSearch($query, $search) {
        $search = "%" . $search . "%";
        $query = $query->where(function ($q) use ($search) {
            $q->orWhere('title', 'LIKE', $search)
                ->orWhere('description', 'LIKE', $search)
                ->orWhere('tags', 'LIKE', $search);
        });
        return $query;
    }

    public function scopeSort($query, $column, $order) {
        if ($column == "category_name") {
            return $query->leftJoin('categories', 'categories.id', '=', 'blogs.category_id')
                ->orderBy('categories.name', $order)
                ->select('blogs.*');
        }
        return $query->orderBy($column, $order);
    }

}
