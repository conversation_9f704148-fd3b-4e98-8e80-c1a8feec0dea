/*!
 * FilePondPluginPdfPreview 1.0.2
 * Licensed under MIT, https://opensource.org/licenses/MIT/
 * Please visit undefined for details.
 */

/* eslint-disable */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).FilePondPluginPdfPreview=t()}(this,function(){"use strict";const e=e=>/pdf$/.test(e.type),t=t=>{return t.utils.createView({name:"pdf-preview-wrapper",create:({root:i,props:r})=>{const d=(t=>t.utils.createView({name:"pdf-preview",tag:"div",ignoreRect:!0,create:({root:t,props:i})=>{const r=t.query("GET_ITEM",{id:i.id});if(e(r.file)){const e=t.query("GET_PDF_PREVIEW_HEIGHT");t.ref.pdf=document.createElement("object"),t.ref.pdf.setAttribute("height",e),t.ref.pdf.setAttribute("width","100%"),t.ref.pdf.setAttribute("style","position:absolute;left:0;right:0;margin:auto;padding-top:unset;"+(e?"height:"+e+"px;":"")),t.element.appendChild(t.ref.pdf)}},write:t.utils.createRoute({DID_PDF_PREVIEW_LOAD:({root:t,props:i})=>{const{id:r}=i,d=t.query("GET_ITEM",{id:r});if(!d)return;let o=window.URL||window.webkitURL,n=new Blob([d.file],{type:d.file.type});if(t.ref.pdf.type=d.file.type,e(d.file)){const e=t.query("GET_PDF_COMPONENT_EXTRA_PARAMS");t.ref.pdf.data=o.createObjectURL(n)+(e?"#?"+e:"")}t.ref.pdf.addEventListener("load",()=>{e(d.file)&&t.dispatch("DID_UPDATE_PANEL_HEIGHT",{id:r,height:t.ref.pdf.scrollHeight})},!1)}})}))(t);i.ref.pdf=i.appendChildView(i.createChildView(d,{id:r.id}))},write:t.utils.createRoute({DID_PDF_PREVIEW_CONTAINER_CREATE:({root:e,props:t})=>{const{id:i}=t;e.query("GET_ITEM",i)&&e.dispatch("DID_PDF_PREVIEW_LOAD",{id:i})}})})},i=i=>{const{addFilter:r,utils:d}=i,{Type:o,createRoute:n}=d,p=t(i);return r("CREATE_VIEW",t=>{const{is:i,view:r,query:d}=t;if(!i("file"))return;r.registerWriter(n({DID_LOAD_ITEM:({root:t,props:i})=>{const{id:o}=i,n=d("GET_ITEM",o);n&&!n.archived&&e(n.file)&&(t.ref.PdfPreview=r.appendChildView(r.createChildView(p,{id:o})),t.dispatch("DID_PDF_PREVIEW_CONTAINER_CREATE",{id:o}))}},({root:t,props:i})=>{const{id:r}=i,o=d("GET_ITEM",r);e(o.file)&&t.rect.element.hidden}))}),{options:{allowPdfPreview:[!0,o.BOOLEAN],pdfPreviewHeight:[320,o.INT],pdfComponentExtraParams:["toolbar=0&navpanes=0&scrollbar=0&statusbar=0&zoom=0&messages=0&view=fitH&page=1",o.STRING]}}};return"undefined"!=typeof window&&void 0!==window.document&&document.dispatchEvent(new CustomEvent("FilePond:pluginloaded",{detail:i})),i});