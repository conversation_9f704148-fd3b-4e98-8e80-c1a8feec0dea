<?php
/**
 * eClassify Travel Offers - Create Admin User
 * Creates a new admin user for the system
 * 
 * <AUTHOR>
 * @project eClassify Travel Offers
 */

// Include Laravel bootstrap
require_once '../bootstrap/app.php';

echo "<h1>👤 eClassify Travel Offers - Create Admin User</h1>";

try {
    // Boot Laravel
    $app = require_once '../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
    
    echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>🔧 Creating Admin User</h2>";
    
    // Database connection test
    try {
        $pdo = new PDO(
            'mysql:host=localhost;dbname=ebroker_clsfd',
            'root',
            '',
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        echo "<p>✅ Database connection successful</p>";
        
        // Check if users table exists
        $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Users table exists</p>";
            
            // Create admin user
            $email = '<EMAIL>';
            $password = password_hash('admin123456', PASSWORD_DEFAULT);
            $name = 'AmrDev Admin';
            
            // Check if user already exists
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->rowCount() > 0) {
                // Update existing user
                $stmt = $pdo->prepare("UPDATE users SET password = ?, name = ? WHERE email = ?");
                $result = $stmt->execute([$password, $name, $email]);
                
                if ($result) {
                    echo "<p>✅ Admin user updated successfully!</p>";
                } else {
                    echo "<p>❌ Failed to update admin user</p>";
                }
            } else {
                // Create new user
                $stmt = $pdo->prepare("INSERT INTO users (name, email, password, email_verified_at, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW(), NOW())");
                $result = $stmt->execute([$name, $email, $password]);
                
                if ($result) {
                    echo "<p>✅ Admin user created successfully!</p>";
                    $userId = $pdo->lastInsertId();
                    
                    // Add admin role if roles table exists
                    $stmt = $pdo->query("SHOW TABLES LIKE 'model_has_roles'");
                    if ($stmt->rowCount() > 0) {
                        // Get Super Admin role ID
                        $stmt = $pdo->prepare("SELECT id FROM roles WHERE name = 'Super Admin' LIMIT 1");
                        $stmt->execute();
                        $role = $stmt->fetch();
                        
                        if ($role) {
                            $stmt = $pdo->prepare("INSERT IGNORE INTO model_has_roles (role_id, model_type, model_id) VALUES (?, 'App\\Models\\User', ?)");
                            $stmt->execute([$role['id'], $userId]);
                            echo "<p>✅ Super Admin role assigned</p>";
                        }
                    }
                } else {
                    echo "<p>❌ Failed to create admin user</p>";
                }
            }
            
        } else {
            echo "<p>❌ Users table not found. Please run migrations first.</p>";
        }
        
    } catch (PDOException $e) {
        echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
        echo "<p>Please check your database configuration in .env file</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #fef2f2; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h2>❌ Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<div style='background: #dcfce7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>✅ Login Credentials</h2>";
echo "<p><strong>Email:</strong> <EMAIL></p>";
echo "<p><strong>Password:</strong> admin123456</p>";
echo "<p><strong>Role:</strong> Super Admin</p>";
echo "</div>";

echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔗 Quick Links</h2>";
echo "<p><a href='/' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Login to Admin Panel</a></p>";
echo "<p><a href='reset-password.php' style='background: #0369a1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Reset Password Tool</a></p>";
echo "<p><a href='install/finish' style='background: #7c3aed; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Complete Installation</a></p>";
echo "</div>";

echo "<div style='background: #e0f2fe; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🔧 Alternative Methods</h2>";
echo "<h3>Method 1: Laravel Artisan</h3>";
echo "<code style='background: #f3f4f6; padding: 10px; display: block; margin: 10px 0;'>";
echo "php artisan tinker<br>";
echo "\$user = new App\Models\User();<br>";
echo "\$user->name = 'AmrDev Admin';<br>";
echo "\$user->email = '<EMAIL>';<br>";
echo "\$user->password = Hash::make('admin123456');<br>";
echo "\$user->save();";
echo "</code>";

echo "<h3>Method 2: Database Direct</h3>";
echo "<code style='background: #f3f4f6; padding: 10px; display: block; margin: 10px 0;'>";
echo "UPDATE users SET password = '" . password_hash('admin123456', PASSWORD_DEFAULT) . "' WHERE email = '<EMAIL>';";
echo "</code>";
echo "</div>";

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<p><strong>eClassify Travel Offers</strong> - Admin User Management</p>";
echo "<p>Developed by AmrDev | Secure Authentication</p>";
echo "</div>";
?>
