@extends('layouts.admin')

@section('title', 'إدارة مقاعد الرحلة - ' . $flight->flight_number)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chair"></i>
                        إدارة مقاعد الرحلة {{ $flight->flight_number }}
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.flights.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i>
                            العودة للرحلات
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- معلومات الرحلة -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="fas fa-info-circle"></i> معلومات الرحلة</h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>رقم الرحلة:</strong> {{ $flight->flight_number }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>شركة الطيران:</strong> {{ $flight->airline->name }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>المسار:</strong> {{ $flight->fromAirport->code }} → {{ $flight->toAirport->code }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>التاريخ:</strong> {{ $flight->departure_date->format('Y-m-d') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات المقاعد -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-chair"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">إجمالي المقاعد</span>
                                    <span class="info-box-number">{{ $flight->total_seats }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-check"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">متاح</span>
                                    <span class="info-box-number">{{ $availableSeats }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-user"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">محجوز</span>
                                    <span class="info-box-number">{{ $bookedSeats }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-times"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">غير متاح</span>
                                    <span class="info-box-number">{{ $unavailableSeats }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أدوات إدارة المقاعد -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="btn-toolbar" role="toolbar">
                                <div class="btn-group mr-2" role="group">
                                    <button type="button" class="btn btn-primary" onclick="generateSeats()">
                                        <i class="fas fa-magic"></i>
                                        إنشاء مقاعد تلقائي
                                    </button>
                                    <button type="button" class="btn btn-success" onclick="addSeat()">
                                        <i class="fas fa-plus"></i>
                                        إضافة مقعد
                                    </button>
                                </div>
                                <div class="btn-group mr-2" role="group">
                                    <button type="button" class="btn btn-warning" onclick="bulkUpdatePrices()">
                                        <i class="fas fa-dollar-sign"></i>
                                        تحديث الأسعار
                                    </button>
                                    <button type="button" class="btn btn-info" onclick="exportSeats()">
                                        <i class="fas fa-download"></i>
                                        تصدير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- خريطة المقاعد -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>خريطة المقاعد</h5>
                                    <div class="card-tools">
                                        <!-- مفتاح الألوان -->
                                        <div class="btn-group btn-group-sm">
                                            <span class="btn btn-success btn-sm">متاح</span>
                                            <span class="btn btn-warning btn-sm">محجوز</span>
                                            <span class="btn btn-danger btn-sm">غير متاح</span>
                                            <span class="btn btn-info btn-sm">درجة أولى</span>
                                            <span class="btn btn-secondary btn-sm">رجال أعمال</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="seat-map" class="seat-map">
                                        @if($seats->count() > 0)
                                            @php
                                                $seatsByRow = $seats->groupBy('seat_row');
                                                $maxSeatsPerRow = $seatsByRow->map->count()->max();
                                            @endphp
                                            
                                            @foreach($seatsByRow as $row => $rowSeats)
                                                <div class="seat-row mb-2">
                                                    <span class="row-label">{{ $row }}</span>
                                                    @foreach($rowSeats->sortBy('seat_column') as $seat)
                                                        <div class="seat-item {{ $seat->status }} {{ $seat->seatType->name ?? 'economy' }}" 
                                                             data-seat-id="{{ $seat->id }}"
                                                             data-seat-number="{{ $seat->seat_number }}"
                                                             title="مقعد {{ $seat->seat_number }} - {{ $seat->seatType->name ?? 'اقتصادي' }} - {{ $seat->price }} د.ك">
                                                            {{ $seat->seat_column }}
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="text-center py-5">
                                                <i class="fas fa-chair fa-3x text-muted mb-3"></i>
                                                <h5>لا توجد مقاعد محددة لهذه الرحلة</h5>
                                                <p class="text-muted">استخدم زر "إنشاء مقاعد تلقائي" لإنشاء مقاعد الطائرة</p>
                                                <button type="button" class="btn btn-primary" onclick="generateSeats()">
                                                    <i class="fas fa-magic"></i>
                                                    إنشاء مقاعد تلقائي
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- جدول المقاعد التفصيلي -->
                    @if($seats->count() > 0)
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5>تفاصيل المقاعد</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped" id="seats-table">
                                            <thead>
                                                <tr>
                                                    <th>رقم المقعد</th>
                                                    <th>الصف</th>
                                                    <th>العمود</th>
                                                    <th>النوع</th>
                                                    <th>السعر (د.ك)</th>
                                                    <th>الحالة</th>
                                                    <th>محجوز لـ</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($seats as $seat)
                                                <tr>
                                                    <td><strong>{{ $seat->seat_number }}</strong></td>
                                                    <td>{{ $seat->seat_row }}</td>
                                                    <td>{{ $seat->seat_column }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ $seat->seatType->name == 'first' ? 'info' : ($seat->seatType->name == 'business' ? 'secondary' : 'primary') }}">
                                                            {{ $seat->seatType->display_name ?? 'اقتصادي' }}
                                                        </span>
                                                    </td>
                                                    <td>{{ number_format($seat->price, 3) }}</td>
                                                    <td>
                                                        @switch($seat->status)
                                                            @case('available')
                                                                <span class="badge badge-success">متاح</span>
                                                                @break
                                                            @case('booked')
                                                                <span class="badge badge-warning">محجوز</span>
                                                                @break
                                                            @case('unavailable')
                                                                <span class="badge badge-danger">غير متاح</span>
                                                                @break
                                                        @endswitch
                                                    </td>
                                                    <td>
                                                        @if($seat->booking)
                                                            <a href="{{ route('admin.flight-bookings.show', $seat->booking->id) }}">
                                                                {{ $seat->booking->passenger_name }}
                                                            </a>
                                                        @else
                                                            -
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-warning" onclick="editSeat({{ $seat->id }})">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            @if($seat->status == 'available')
                                                                <button class="btn btn-danger" onclick="deleteSeat({{ $seat->id }})">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لإضافة/تعديل مقعد -->
<div class="modal fade" id="seatModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="seatModalTitle">إضافة مقعد</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="seatForm">
                <div class="modal-body">
                    <input type="hidden" id="seat_id" name="seat_id">
                    <div class="form-group">
                        <label>رقم المقعد</label>
                        <input type="text" class="form-control" id="seat_number" name="seat_number" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>الصف</label>
                                <input type="text" class="form-control" id="seat_row" name="seat_row" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>العمود</label>
                                <input type="text" class="form-control" id="seat_column" name="seat_column" required>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>نوع المقعد</label>
                        <select class="form-control" id="seat_type_id" name="seat_type_id" required>
                            @foreach($seatTypes as $type)
                                <option value="{{ $type->id }}">{{ $type->display_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label>السعر (د.ك)</label>
                        <input type="number" step="0.001" class="form-control" id="price" name="price" required>
                    </div>
                    <div class="form-group">
                        <label>الحالة</label>
                        <select class="form-control" id="status" name="status">
                            <option value="available">متاح</option>
                            <option value="unavailable">غير متاح</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.seat-map {
    text-align: center;
    padding: 20px;
}

.seat-row {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 5px;
}

.row-label {
    width: 30px;
    font-weight: bold;
    margin-right: 10px;
}

.seat-item {
    width: 35px;
    height: 35px;
    margin: 2px;
    border: 2px solid #ddd;
    border-radius: 5px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
}

.seat-item.available {
    background-color: #28a745;
    color: white;
    border-color: #1e7e34;
}

.seat-item.booked {
    background-color: #ffc107;
    color: #212529;
    border-color: #d39e00;
}

.seat-item.unavailable {
    background-color: #dc3545;
    color: white;
    border-color: #bd2130;
}

.seat-item.first {
    border-color: #17a2b8;
    box-shadow: 0 0 5px rgba(23, 162, 184, 0.5);
}

.seat-item.business {
    border-color: #6c757d;
    box-shadow: 0 0 5px rgba(108, 117, 125, 0.5);
}

.seat-item:hover {
    transform: scale(1.1);
    z-index: 10;
}
</style>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    $('#seats-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "pageLength": 50
    });

    // Seat click handler
    $('.seat-item').on('click', function() {
        const seatId = $(this).data('seat-id');
        editSeat(seatId);
    });
});

function generateSeats() {
    if (confirm('هل تريد إنشاء مقاعد تلقائياً؟ سيتم إنشاء مقاعد بناءً على إجمالي المقاعد المحدد.')) {
        $.ajax({
            url: '{{ route("admin.flights.generate-seats", $flight->id) }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + response.message);
                }
            }
        });
    }
}

function addSeat() {
    $('#seatModalTitle').text('إضافة مقعد جديد');
    $('#seatForm')[0].reset();
    $('#seat_id').val('');
    $('#seatModal').modal('show');
}

function editSeat(seatId) {
    $.ajax({
        url: '/admin/flights/seats/' + seatId,
        type: 'GET',
        success: function(seat) {
            $('#seatModalTitle').text('تعديل المقعد ' + seat.seat_number);
            $('#seat_id').val(seat.id);
            $('#seat_number').val(seat.seat_number);
            $('#seat_row').val(seat.seat_row);
            $('#seat_column').val(seat.seat_column);
            $('#seat_type_id').val(seat.seat_type_id);
            $('#price').val(seat.price);
            $('#status').val(seat.status);
            $('#seatModal').modal('show');
        }
    });
}

function deleteSeat(seatId) {
    if (confirm('هل أنت متأكد من حذف هذا المقعد؟')) {
        $.ajax({
            url: '/admin/flights/seats/' + seatId,
            type: 'DELETE',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ أثناء الحذف');
                }
            }
        });
    }
}

$('#seatForm').on('submit', function(e) {
    e.preventDefault();
    
    const seatId = $('#seat_id').val();
    const url = seatId ? '/admin/flights/seats/' + seatId : '{{ route("admin.flights.store-seat", $flight->id) }}';
    const method = seatId ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        type: method,
        data: $(this).serialize() + '&_token={{ csrf_token() }}',
        success: function(response) {
            if (response.success) {
                $('#seatModal').modal('hide');
                location.reload();
            } else {
                alert('حدث خطأ: ' + response.message);
            }
        }
    });
});
</script>
@endsection
