<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Event extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'title_ar',
        'slug',
        'content',
        'content_ar',
        'image_id',
        'banner_image_id',
        'venue_id',
        'address',
        'map_lat',
        'map_lng',
        'map_zoom',
        'is_featured',
        'gallery',
        'video',
        'faqs',
        'ticket_types',
        'duration',
        'start_time',
        'event_type',
        'organizer',
        'price',
        'sale_price',
        'is_instant',
        'enable_extra_price',
        'extra_price',
        'review_score',
        'ical_import_url',
        'status',
        'default_state',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'review_score' => 'decimal:1',
        'is_featured' => 'boolean',
        'is_instant' => 'boolean',
        'enable_extra_price' => 'boolean',
        'gallery' => 'array',
        'faqs' => 'array',
        'ticket_types' => 'array',
        'extra_price' => 'array'
    ];

    protected $appends = ['display_title', 'display_content', 'status_text', 'event_type_text'];

    /**
     * Get display title based on locale
     */
    public function getDisplayTitleAttribute()
    {
        return app()->getLocale() === 'ar' ? ($this->title_ar ?: $this->title) : $this->title;
    }

    /**
     * Get display content based on locale
     */
    public function getDisplayContentAttribute()
    {
        return app()->getLocale() === 'ar' ? ($this->content_ar ?: $this->content) : $this->content;
    }

    /**
     * Get status text
     */
    public function getStatusTextAttribute()
    {
        return match($this->status) {
            'upcoming' => 'قادم',
            'ongoing' => 'جاري',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            default => 'غير محدد'
        };
    }

    /**
     * Get event type text
     */
    public function getEventTypeTextAttribute()
    {
        return match($this->event_type) {
            'football' => 'كرة قدم',
            'basketball' => 'كرة سلة',
            'concert' => 'حفل موسيقي',
            'conference' => 'مؤتمر',
            'other' => 'أخرى',
            default => 'غير محدد'
        };
    }

    /**
     * Generate slug from title
     */
    public static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (empty($event->slug)) {
                $event->slug = Str::slug($event->title);
            }
        });
    }

    /**
     * Get venue
     */
    public function venue()
    {
        return $this->belongsTo(Venue::class);
    }

    /**
     * Get event dates
     */
    public function eventDates()
    {
        return $this->hasMany(EventDate::class, 'target_id');
    }

    /**
     * Get active event dates
     */
    public function activeEventDates()
    {
        return $this->hasMany(EventDate::class, 'target_id')->where('active', 1);
    }

    /**
     * Get ticket categories
     */
    public function ticketCategories()
    {
        return $this->hasMany(TicketCategory::class);
    }

    /**
     * Get available ticket categories
     */
    public function availableTicketCategories()
    {
        return $this->hasMany(TicketCategory::class)->where('available_quantity', '>', 0);
    }

    /**
     * Get ticket bookings
     */
    public function ticketBookings()
    {
        return $this->hasMany(TicketBooking::class);
    }

    /**
     * Get confirmed bookings
     */
    public function confirmedBookings()
    {
        return $this->hasMany(TicketBooking::class)->where('status', 'confirmed');
    }

    /**
     * Get translations
     */
    public function translations()
    {
        return $this->hasMany(EventTranslation::class, 'origin_id');
    }

    /**
     * Get terms
     */
    public function terms()
    {
        return $this->hasMany(EventTerm::class, 'target_id');
    }

    /**
     * Scope for upcoming events
     */
    public function scopeUpcoming($query)
    {
        return $query->where('status', 'upcoming');
    }

    /**
     * Scope for featured events
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope by event type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('event_type', $type);
    }

    /**
     * Scope by venue
     */
    public function scopeByVenue($query, $venueId)
    {
        return $query->where('venue_id', $venueId);
    }

    /**
     * Get creator user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    /**
     * Get updater user
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
