<?php
/**
 * Seller Verification Debug Tool
 * تشخيص مشاكل seller-verification
 */

// Include Lara<PERSON> bootstrap
require_once '../vendor/autoload.php';

$app = require_once '../bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص Seller Verification</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-box { padding: 15px; margin: 15px 0; border-radius: 8px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1, h2, h3 { color: #333; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #005a8b; }
        code { background: #f8f9fa; padding: 3px 6px; border-radius: 3px; font-family: monospace; }
        .section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: right; }
        th { background: #f8f9fa; }
        .route-info { font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; }
        .test-form { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 تشخيص Seller Verification</h1>
        
        <?php
        // Test 1: Check Routes
        echo '<div class="section">';
        echo '<h2>1️⃣ فحص الـ Routes</h2>';
        
        try {
            $routes = app('router')->getRoutes();
            $sellerVerificationRoutes = [];
            
            foreach ($routes as $route) {
                $uri = $route->uri();
                if (strpos($uri, 'seller-verification') !== false) {
                    $sellerVerificationRoutes[] = [
                        'method' => implode('|', $route->methods()),
                        'uri' => $uri,
                        'name' => $route->getName(),
                        'action' => $route->getActionName()
                    ];
                }
            }
            
            echo '<div class="status-box success">✅ تم العثور على ' . count($sellerVerificationRoutes) . ' routes</div>';
            
            echo '<table>';
            echo '<tr><th>Method</th><th>URI</th><th>Name</th><th>Action</th></tr>';
            foreach ($sellerVerificationRoutes as $route) {
                echo '<tr>';
                echo '<td>' . $route['method'] . '</td>';
                echo '<td>' . $route['uri'] . '</td>';
                echo '<td>' . ($route['name'] ?: '-') . '</td>';
                echo '<td>' . $route['action'] . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
        } catch (Exception $e) {
            echo '<div class="status-box error">❌ خطأ في فحص الـ routes: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';
        
        // Test 2: Check Controller
        echo '<div class="section">';
        echo '<h2>2️⃣ فحص Controller</h2>';
        
        try {
            $controllerExists = class_exists('App\Http\Controllers\UserVerificationController');
            if ($controllerExists) {
                echo '<div class="status-box success">✅ UserVerificationController موجود</div>';
                
                $controller = new ReflectionClass('App\Http\Controllers\UserVerificationController');
                $methods = $controller->getMethods(ReflectionMethod::IS_PUBLIC);
                
                echo '<h4>الـ Methods المتاحة:</h4>';
                echo '<ul>';
                foreach ($methods as $method) {
                    if ($method->class === 'App\Http\Controllers\UserVerificationController') {
                        echo '<li><code>' . $method->name . '</code></li>';
                    }
                }
                echo '</ul>';
                
            } else {
                echo '<div class="status-box error">❌ UserVerificationController غير موجود</div>';
            }
        } catch (Exception $e) {
            echo '<div class="status-box error">❌ خطأ في فحص Controller: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';
        
        // Test 3: Check Models
        echo '<div class="section">';
        echo '<h2>3️⃣ فحص Models</h2>';
        
        $models = ['VerificationField', 'VerificationRequest', 'VerificationFieldValue'];
        foreach ($models as $model) {
            try {
                $modelClass = 'App\Models\\' . $model;
                if (class_exists($modelClass)) {
                    echo '<div class="status-box success">✅ ' . $model . ' موجود</div>';
                    
                    // Check table exists
                    $instance = new $modelClass;
                    $table = $instance->getTable();
                    echo '<div class="info">📋 Table: ' . $table . '</div>';
                    
                } else {
                    echo '<div class="status-box error">❌ ' . $model . ' غير موجود</div>';
                }
            } catch (Exception $e) {
                echo '<div class="status-box error">❌ خطأ في ' . $model . ': ' . $e->getMessage() . '</div>';
            }
        }
        echo '</div>';
        
        // Test 4: Check Database Tables
        echo '<div class="section">';
        echo '<h2>4️⃣ فحص قاعدة البيانات</h2>';
        
        try {
            $tables = ['verification_fields', 'verification_requests', 'verification_field_values'];
            foreach ($tables as $table) {
                try {
                    $exists = DB::select("SHOW TABLES LIKE '$table'");
                    if (!empty($exists)) {
                        echo '<div class="status-box success">✅ جدول ' . $table . ' موجود</div>';
                        
                        // Get table structure
                        $columns = DB::select("DESCRIBE $table");
                        echo '<details><summary>عرض أعمدة الجدول</summary>';
                        echo '<table>';
                        echo '<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>';
                        foreach ($columns as $column) {
                            echo '<tr>';
                            echo '<td>' . $column->Field . '</td>';
                            echo '<td>' . $column->Type . '</td>';
                            echo '<td>' . $column->Null . '</td>';
                            echo '<td>' . $column->Key . '</td>';
                            echo '</tr>';
                        }
                        echo '</table>';
                        echo '</details>';
                        
                    } else {
                        echo '<div class="status-box error">❌ جدول ' . $table . ' غير موجود</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="status-box error">❌ خطأ في فحص جدول ' . $table . ': ' . $e->getMessage() . '</div>';
                }
            }
        } catch (Exception $e) {
            echo '<div class="status-box error">❌ خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';
        
        // Test 5: Check Permissions
        echo '<div class="section">';
        echo '<h2>5️⃣ فحص الصلاحيات</h2>';
        
        try {
            $permissions = [
                'seller-verification-field-create',
                'seller-verification-field-update', 
                'seller-verification-field-delete',
                'seller-verification-request-update'
            ];
            
            foreach ($permissions as $permission) {
                try {
                    $exists = DB::table('permissions')->where('name', $permission)->exists();
                    if ($exists) {
                        echo '<div class="status-box success">✅ صلاحية ' . $permission . ' موجودة</div>';
                    } else {
                        echo '<div class="status-box warning">⚠️ صلاحية ' . $permission . ' غير موجودة</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="status-box error">❌ خطأ في فحص صلاحية ' . $permission . '</div>';
                }
            }
        } catch (Exception $e) {
            echo '<div class="status-box error">❌ خطأ في فحص الصلاحيات: ' . $e->getMessage() . '</div>';
        }
        echo '</div>';
        
        // Test 6: AJAX Test Form
        echo '<div class="section">';
        echo '<h2>6️⃣ اختبار AJAX</h2>';
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_ajax'])) {
            echo '<div class="status-box info">🔄 اختبار AJAX...</div>';
            
            $testData = [
                'name' => 'Test Field',
                'type' => 'textbox',
                'status' => 1,
                'min_length' => 1,
                'max_length' => 100
            ];
            
            echo '<div class="status-box info">📤 البيانات المرسلة:</div>';
            echo '<pre>' . json_encode($testData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . '</pre>';
        }
        
        echo '<div class="test-form">';
        echo '<h4>اختبار إرسال البيانات</h4>';
        echo '<form method="POST">';
        echo '<input type="hidden" name="test_ajax" value="1">';
        echo '<button type="submit" class="btn">اختبار AJAX</button>';
        echo '</form>';
        echo '</div>';
        echo '</div>';
        
        // Test 7: JavaScript Check
        echo '<div class="section">';
        echo '<h2>7️⃣ فحص JavaScript</h2>';
        
        $jsFiles = [
            'public/assets/js/custom/common.js',
            'public/assets/js/custom/function.js',
            'public/assets/js/custom/bootstrap-table/actionEvents.js'
        ];
        
        foreach ($jsFiles as $file) {
            if (file_exists('../' . $file)) {
                echo '<div class="status-box success">✅ ' . $file . ' موجود</div>';
            } else {
                echo '<div class="status-box error">❌ ' . $file . ' غير موجود</div>';
            }
        }
        echo '</div>';
        
        // Recommendations
        echo '<div class="section">';
        echo '<h2>💡 التوصيات</h2>';
        echo '<div class="status-box info">';
        echo '<h4>خطوات حل المشكلة:</h4>';
        echo '<ol>';
        echo '<li>تحقق من وجود جميع الـ routes المطلوبة</li>';
        echo '<li>تأكد من وجود الصلاحيات المناسبة للمستخدم</li>';
        echo '<li>تحقق من console المتصفح للأخطاء JavaScript</li>';
        echo '<li>تحقق من Network tab لرؤية الـ AJAX requests</li>';
        echo '<li>تحقق من logs Laravel في storage/logs/laravel.log</li>';
        echo '</ol>';
        echo '</div>';
        echo '</div>';
        ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="/seller-verification" class="btn">🔙 العودة لـ Seller Verification</a>
            <a href="/" class="btn">🏠 العودة للوحة التحكم</a>
        </div>
    </div>
    
    <script>
        // Check for JavaScript errors
        window.addEventListener('error', function(e) {
            console.error('JavaScript Error:', e.error);
        });
        
        // Check if jQuery is loaded
        if (typeof jQuery === 'undefined') {
            console.error('jQuery is not loaded!');
        } else {
            console.log('jQuery version:', jQuery.fn.jquery);
        }
        
        // Check CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (!csrfToken) {
            console.error('CSRF token not found!');
        } else {
            console.log('CSRF token found');
        }
    </script>
</body>
</html>
