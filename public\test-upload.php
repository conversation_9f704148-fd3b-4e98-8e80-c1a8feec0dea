<?php
// Test file upload functionality
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع الصور</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"] { width: 100%; padding: 10px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .error { color: red; }
        .success { color: green; }
        img { max-width: 300px; height: auto; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار رفع الصور</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['image'])) {
            $uploadDir = '../storage/app/public/test/';
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
            $maxSize = 5 * 1024 * 1024; // 5MB
            
            $file = $_FILES['image'];
            $fileName = $file['name'];
            $fileTmpName = $file['tmp_name'];
            $fileSize = $file['size'];
            $fileType = $file['type'];
            $fileError = $file['error'];
            
            echo '<div class="result">';
            
            if ($fileError === 0) {
                if (in_array($fileType, $allowedTypes)) {
                    if ($fileSize <= $maxSize) {
                        $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
                        $newFileName = uniqid('test_', true) . '.' . $fileExtension;
                        $uploadPath = $uploadDir . $newFileName;
                        
                        if (!file_exists($uploadDir)) {
                            mkdir($uploadDir, 0755, true);
                        }
                        
                        if (move_uploaded_file($fileTmpName, $uploadPath)) {
                            echo '<p class="success">✅ تم رفع الصورة بنجاح!</p>';
                            echo '<p><strong>اسم الملف:</strong> ' . $newFileName . '</p>';
                            echo '<p><strong>المسار:</strong> storage/test/' . $newFileName . '</p>';
                            echo '<p><strong>رابط الصورة:</strong> <a href="storage/test/' . $newFileName . '" target="_blank">عرض الصورة</a></p>';
                            echo '<img src="storage/test/' . $newFileName . '" alt="الصورة المرفوعة">';
                        } else {
                            echo '<p class="error">❌ فشل في رفع الصورة!</p>';
                        }
                    } else {
                        echo '<p class="error">❌ حجم الملف كبير جداً! الحد الأقصى 5MB</p>';
                    }
                } else {
                    echo '<p class="error">❌ نوع الملف غير مدعوم! يُسمح فقط بـ JPG, PNG, GIF</p>';
                }
            } else {
                echo '<p class="error">❌ خطأ في رفع الملف: ' . $fileError . '</p>';
            }
            
            echo '</div>';
        }
        ?>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="image">اختر صورة للرفع:</label>
                <input type="file" name="image" id="image" accept="image/*" required>
            </div>
            <button type="submit">رفع الصورة</button>
        </form>
        
        <div style="margin-top: 30px; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff;">
            <h3>معلومات مهمة:</h3>
            <ul>
                <li>تأكد من وجود مجلد storage/app/public</li>
                <li>تأكد من إنشاء symbolic link: <code>php artisan storage:link</code></li>
                <li>تأكد من صلاحيات الكتابة على مجلد storage</li>
                <li>تحقق من إعدادات FILESYSTEM_DISK في ملف .env</li>
            </ul>
        </div>
    </div>
</body>
</html>
