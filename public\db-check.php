<?php
/**
 * eClassify Travel Offers - Database Check
 * Check database connection and user tables
 * 
 * <AUTHOR>
 * @project eClassify Travel Offers
 */

echo "<h1>🗄️ eClassify Travel Offers - Database Check</h1>";

echo "<div style='background: #f0f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📊 Database Connection Test</h2>";

try {
    // Test database connection
    $pdo = new PDO(
        'mysql:host=localhost;dbname=ebroker_clsfd',
        'root',
        '',
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "<p>✅ Database connection successful</p>";
    echo "<p><strong>Database:</strong> ebroker_clsfd</p>";
    echo "<p><strong>Host:</strong> localhost</p>";
    
    // Check tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>📋 Available Tables (" . count($tables) . "):</h3>";
    echo "<div style='background: white; padding: 15px; border-radius: 8px; border: 1px solid #ddd;'>";
    
    $important_tables = ['users', 'roles', 'model_has_roles', 'permissions'];
    foreach ($important_tables as $table) {
        $exists = in_array($table, $tables);
        echo "<p><strong>$table:</strong> " . ($exists ? '✅ Exists' : '❌ Missing') . "</p>";
    }
    
    echo "<details style='margin-top: 10px;'>";
    echo "<summary>Show all tables</summary>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    echo "</details>";
    echo "</div>";
    
    // Check users table specifically
    if (in_array('users', $tables)) {
        echo "<h3>👥 Users Table Analysis:</h3>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM users");
        $total_users = $stmt->fetch()['total'];
        echo "<p><strong>Total Users:</strong> $total_users</p>";
        
        if ($total_users > 0) {
            $stmt = $pdo->query("SELECT id, name, email, created_at FROM users ORDER BY id ASC LIMIT 5");
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table style='width: 100%; border-collapse: collapse; background: white; margin: 10px 0;'>";
            echo "<tr style='background: #f3f4f6;'>";
            echo "<th style='padding: 10px; border: 1px solid #ddd;'>ID</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd;'>Name</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd;'>Email</th>";
            echo "<th style='padding: 10px; border: 1px solid #ddd;'>Created</th>";
            echo "</tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $user['id'] . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($user['name']) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . htmlspecialchars($user['email']) . "</td>";
                echo "<td style='padding: 10px; border: 1px solid #ddd;'>" . $user['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>⚠️ No users found in database. You need to create an admin user.</p>";
        }
    }
    
    // Check roles if table exists
    if (in_array('roles', $tables)) {
        echo "<h3>🔐 Roles Table:</h3>";
        $stmt = $pdo->query("SELECT id, name FROM roles");
        $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($roles) {
            echo "<ul>";
            foreach ($roles as $role) {
                echo "<li><strong>" . $role['name'] . "</strong> (ID: " . $role['id'] . ")</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>⚠️ No roles found. You may need to run seeders.</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Database connection failed</p>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    
    echo "<h3>🔧 Possible Solutions:</h3>";
    echo "<ul>";
    echo "<li>Check if MySQL is running</li>";
    echo "<li>Verify database name: ebroker_clsfd</li>";
    echo "<li>Check username/password in .env file</li>";
    echo "<li>Create database if it doesn't exist</li>";
    echo "</ul>";
}

echo "</div>";

echo "<div style='background: #dcfce7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🚀 Quick Actions</h2>";
echo "<p><a href='create-admin.php' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Create Admin User</a></p>";
echo "<p><a href='reset-password.php' style='background: #0369a1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Reset Password</a></p>";
echo "<p><a href='/' style='background: #7c3aed; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Try Login</a></p>";
echo "</div>";

echo "<div style='background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>📋 Manual Database Commands</h2>";
echo "<h3>Create Admin User:</h3>";
echo "<code style='background: #f3f4f6; padding: 10px; display: block; margin: 10px 0;'>";
echo "INSERT INTO users (name, email, password, email_verified_at, created_at, updated_at) VALUES <br>";
echo "('AmrDev Admin', '<EMAIL>', '" . password_hash('admin123456', PASSWORD_DEFAULT) . "', NOW(), NOW(), NOW());";
echo "</code>";

echo "<h3>Reset Password:</h3>";
echo "<code style='background: #f3f4f6; padding: 10px; display: block; margin: 10px 0;'>";
echo "UPDATE users SET password = '" . password_hash('admin123456', PASSWORD_DEFAULT) . "' WHERE email = '<EMAIL>';";
echo "</code>";
echo "</div>";

echo "<div style='background: #f3f4f6; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: center;'>";
echo "<p><strong>eClassify Travel Offers</strong> - Database Management</p>";
echo "<p>Developed by AmrDev | Data Integrity Check</p>";
echo "</div>";
?>
