<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Venue extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'address',
        'city',
        'country',
        'capacity',
        'map_lat',
        'map_lng',
        'map_zoom',
        'facilities',
        'images',
        'status',
        'create_user',
        'update_user'
    ];

    protected $casts = [
        'facilities' => 'array',
        'images' => 'array'
    ];

    protected $appends = ['display_name'];

    /**
     * Get display name based on locale
     */
    public function getDisplayNameAttribute()
    {
        return app()->getLocale() === 'ar' ? ($this->name_ar ?: $this->name) : $this->name;
    }

    /**
     * Get events at this venue
     */
    public function events()
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Get upcoming events
     */
    public function upcomingEvents()
    {
        return $this->hasMany(Event::class)->where('status', 'upcoming');
    }

    /**
     * Get active events
     */
    public function activeEvents()
    {
        return $this->hasMany(Event::class)->whereIn('status', ['upcoming', 'ongoing']);
    }

    /**
     * Scope for active venues
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope by city
     */
    public function scopeByCity($query, $city)
    {
        return $query->where('city', $city);
    }

    /**
     * Scope by country
     */
    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Get creator user
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'create_user');
    }

    /**
     * Get updater user
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'update_user');
    }
}
